<?php
/**
 * Test script for Recharge API
 * Use this to test the recharge API endpoints
 */

// Define admin access constant for config.php
define('ADMIN_ACCESS', true);

// Include configuration
require_once 'config.php';

// Set content type
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Recharge API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 8px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 Recharge API Test Panel</h1>
    
    <div class="test-section info">
        <h3>📊 Database Status</h3>
        <?php
        try {
            $conn = getDbConnection();
            echo "<p>✅ Database connection: <strong>SUCCESS</strong></p>";
            
            // Check tables
            $tables = ['licenses', 'auth_logs', 'operators', 'recharge_logs', 'recharge_settings', 'recharge_sms_logs'];
            echo "<h4>Table Status:</h4><ul>";
            foreach ($tables as $table) {
                $result = $conn->query("SHOW TABLES LIKE '$table'");
                $status = $result->num_rows > 0 ? '✅ EXISTS' : '❌ MISSING';
                echo "<li><strong>$table</strong>: $status</li>";
            }
            echo "</ul>";
            
        } catch (Exception $e) {
            echo "<p>❌ Database connection: <strong>FAILED</strong></p>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>

    <div class="test-section">
        <h3>🧪 API Test Functions</h3>
        
        <button onclick="testApi()">Test API Connection</button>
        <button onclick="testOperators()">Test Get Operators</button>
        <button onclick="testSubmitRecharge()">Test Submit Recharge</button>
        
        <div id="test-results" style="margin-top: 20px;"></div>
    </div>

    <div class="test-section">
        <h3>📝 Manual API Test</h3>
        <form id="manual-test-form">
            <p>
                <label>Action:</label><br>
                <select name="action" id="action">
                    <option value="test_api">test_api</option>
                    <option value="get_operators">get_operators</option>
                    <option value="submit_recharge">submit_recharge</option>
                    <option value="get_recharge_settings">get_recharge_settings</option>
                </select>
            </p>
            <p>
                <label>License Key:</label><br>
                <input type="text" name="license_key" placeholder="Enter license key" style="width: 300px;">
            </p>
            <p>
                <label>Device ID:</label><br>
                <input type="text" name="device_id" placeholder="Enter device ID" style="width: 300px;">
            </p>
            <p>
                <label>Phone Number (for recharge):</label><br>
                <input type="text" name="phone_number" placeholder="01712345678" style="width: 300px;">
            </p>
            <p>
                <label>Amount (for recharge):</label><br>
                <input type="number" name="amount" placeholder="50" style="width: 100px;">
            </p>
            <p>
                <label>Operator (for recharge):</label><br>
                <select name="operator">
                    <option value="GP">Grameenphone</option>
                    <option value="ROBI">Robi</option>
                    <option value="BL">Banglalink</option>
                    <option value="AIRTEL">Airtel</option>
                    <option value="TT">Teletalk</option>
                </select>
            </p>
            <p>
                <button type="button" onclick="submitManualTest()">Submit Test</button>
            </p>
        </form>
        
        <div id="manual-results" style="margin-top: 20px;"></div>
    </div>

    <script>
        function testApi() {
            showResult('test-results', 'Testing API connection...', 'info');
            
            fetch('api/recharge_api.php?action=test_api')
                .then(response => response.json())
                .then(data => {
                    showResult('test-results', JSON.stringify(data, null, 2), data.success ? 'success' : 'error');
                })
                .catch(error => {
                    showResult('test-results', 'Error: ' + error.message, 'error');
                });
        }
        
        function testOperators() {
            showResult('test-results', 'Testing get operators...', 'info');
            
            fetch('api/recharge_api.php?action=get_operators&country=BD')
                .then(response => response.json())
                .then(data => {
                    showResult('test-results', JSON.stringify(data, null, 2), data.success ? 'success' : 'error');
                })
                .catch(error => {
                    showResult('test-results', 'Error: ' + error.message, 'error');
                });
        }
        
        function testSubmitRecharge() {
            showResult('test-results', 'Testing submit recharge (will fail without valid license)...', 'info');
            
            const formData = new FormData();
            formData.append('action', 'submit_recharge');
            formData.append('license_key', 'test_license');
            formData.append('device_id', 'test_device');
            formData.append('phone_number', '01712345678');
            formData.append('amount', '50');
            formData.append('operator', 'GP');
            formData.append('sim_slot', '1');
            
            fetch('api/recharge_api.php', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    showResult('test-results', JSON.stringify(data, null, 2), data.success ? 'success' : 'error');
                })
                .catch(error => {
                    showResult('test-results', 'Error: ' + error.message, 'error');
                });
        }
        
        function submitManualTest() {
            const form = document.getElementById('manual-test-form');
            const formData = new FormData(form);
            
            showResult('manual-results', 'Submitting manual test...', 'info');
            
            const method = formData.get('action') === 'get_operators' || formData.get('action') === 'test_api' ? 'GET' : 'POST';
            
            let url = 'api/recharge_api.php';
            if (method === 'GET') {
                const params = new URLSearchParams(formData);
                url += '?' + params.toString();
            }
            
            const options = {
                method: method
            };
            
            if (method === 'POST') {
                options.body = formData;
            }
            
            fetch(url, options)
                .then(response => response.json())
                .then(data => {
                    showResult('manual-results', JSON.stringify(data, null, 2), data.success ? 'success' : 'error');
                })
                .catch(error => {
                    showResult('manual-results', 'Error: ' + error.message, 'error');
                });
        }
        
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}"><pre>${message}</pre></div>`;
        }
    </script>

    <div class="test-section">
        <h3>📚 API Documentation</h3>
        <h4>Available Endpoints:</h4>
        <ul>
            <li><strong>test_api</strong> - Test API connection and database status</li>
            <li><strong>get_operators</strong> - Get list of available operators</li>
            <li><strong>submit_recharge</strong> - Submit a recharge request</li>
            <li><strong>get_recharge_status</strong> - Get status of a recharge order</li>
            <li><strong>update_recharge_status</strong> - Update recharge status</li>
            <li><strong>get_recharge_settings</strong> - Get user recharge settings</li>
            <li><strong>update_recharge_settings</strong> - Update user recharge settings</li>
            <li><strong>get_recharge_history</strong> - Get recharge transaction history</li>
            <li><strong>process_sms_response</strong> - Process SMS response from operator</li>
        </ul>
        
        <h4>Required Parameters:</h4>
        <ul>
            <li><strong>license_key</strong> - Valid license key (required for most operations)</li>
            <li><strong>device_id</strong> - Device identifier (required for most operations)</li>
            <li><strong>action</strong> - API action to perform</li>
        </ul>
    </div>

</body>
</html>
