-- Recharge Module Database Setup
-- Run this script to add recharge functionality tables to the existing database

-- Create recharge transaction logs table
CREATE TABLE IF NOT EXISTS recharge_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    license_key VARCHAR(255) NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    order_id VARCHAR(100) UNIQUE NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    operator VARCHAR(50) NOT NULL,
    ussd_code VARCHAR(100),
    sim_slot INT DEFAULT 1,
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    api_response TEXT,
    sms_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_license_key (license_key),
    INDEX idx_device_id (device_id),
    INDEX idx_order_id (order_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Create operators configuration table
CREATE TABLE IF NOT EXISTS operators (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) NOT NULL UNIQUE,
    ussd_pattern VARCHAR(200) NOT NULL,
    country VARCHAR(50) DEFAULT 'BD',
    status TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_country (country),
    INDEX idx_status (status)
);

-- Create recharge settings per license table
CREATE TABLE IF NOT EXISTS recharge_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    license_key VARCHAR(255) NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    sim1_operator VARCHAR(50),
    sim2_operator VARCHAR(50),
    auto_recharge_enabled TINYINT(1) DEFAULT 0,
    server_url VARCHAR(500),
    api_pin VARCHAR(100),
    settings_json TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_license_device (license_key, device_id),
    INDEX idx_license_key (license_key),
    INDEX idx_device_id (device_id)
);

-- Insert default operators for Bangladesh
INSERT INTO operators (name, code, ussd_pattern, country) VALUES
('Grameenphone', 'GP', '*121*{amount}*{number}#', 'BD'),
('Robi', 'ROBI', '*123*{amount}*{number}#', 'BD'),
('Banglalink', 'BL', '*124*{amount}*{number}#', 'BD'),
('Airtel', 'AIRTEL', '*125*{amount}*{number}#', 'BD'),
('Teletalk', 'TT', '*126*{amount}*{number}#', 'BD')
ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    ussd_pattern = VALUES(ussd_pattern),
    updated_at = CURRENT_TIMESTAMP;

-- Create recharge API logs table for debugging
CREATE TABLE IF NOT EXISTS recharge_api_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    license_key VARCHAR(255),
    device_id VARCHAR(255),
    endpoint VARCHAR(200),
    request_data TEXT,
    response_data TEXT,
    response_code INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_license_key (license_key),
    INDEX idx_device_id (device_id),
    INDEX idx_created_at (created_at)
);

-- Create SMS logs table for recharge responses
CREATE TABLE IF NOT EXISTS recharge_sms_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(255),
    sender VARCHAR(50),
    message_body TEXT,
    order_id VARCHAR(100),
    processed TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_device_id (device_id),
    INDEX idx_order_id (order_id),
    INDEX idx_processed (processed),
    INDEX idx_created_at (created_at)
);

-- Add recharge module settings to existing settings table if it exists
-- This will be handled by the admin panel configuration

-- Create view for recharge statistics
CREATE OR REPLACE VIEW recharge_stats AS
SELECT 
    license_key,
    device_id,
    COUNT(*) as total_recharges,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_recharges,
    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_recharges,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_recharges,
    SUM(amount) as total_amount,
    MAX(created_at) as last_recharge_date
FROM recharge_logs 
GROUP BY license_key, device_id;

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON recharge_logs TO 'your_app_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON operators TO 'your_app_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON recharge_settings TO 'your_app_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON recharge_api_logs TO 'your_app_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON recharge_sms_logs TO 'your_app_user'@'localhost';
-- GRANT SELECT ON recharge_stats TO 'your_app_user'@'localhost';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_recharge_logs_license_status ON recharge_logs(license_key, status);
CREATE INDEX IF NOT EXISTS idx_recharge_logs_device_status ON recharge_logs(device_id, status);
CREATE INDEX IF NOT EXISTS idx_recharge_logs_date_status ON recharge_logs(created_at, status);

-- Success message
SELECT 'Recharge module database tables created successfully!' as message;
