package com.appystore.mrecharge;


import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.database.DatabaseUtils;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.preference.PreferenceManager;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.appystore.mrecharge.service.Recharge;

import java.util.HashMap;
import java.util.List;

/* loaded from: classes.dex */
public class DbHelper extends SQLiteOpenHelper {
    public static final String CONTACTS_COLUMN_AMOUNT = "amount";
    public static final String CONTACTS_COLUMN_ID = "id";
    public static final String CONTACTS_COLUMN_NUMBER = "number";
    public static final String CONTACTS_COLUMN_TIME = "time";
    public static final String CONTACTS_TABLE_NAME = "income_log";
    public static final String DATABASE_NAME = "flexilog.db";
    public static final String Seting_TABLE_NAME = "setting";
    public static final String Status = "status";
    String amount;
    private Context appContext;
    String extra;
    private HashMap hp;
    int id;
    private List<Items> itemsList;
    String number;
    String operator;

    public DbHelper(Context context) {
        super(context, DATABASE_NAME, (SQLiteDatabase.CursorFactory) null, 1);
        this.appContext = context;
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public void onCreate(SQLiteDatabase sQLiteDatabase) {
        sQLiteDatabase.execSQL("create table income_log (id integer PRIMARY KEY AUTOINCREMENT,apiresponse text,orderid text, number text,amount text,pcode text,ussd text, one text,two text,three text,four text,five text,line integer,triger integer ,slot integer,_id integer,time DATETIME DEFAULT CURRENT_TIMESTAMP, status text DEFAULT 0, ostatus text DEFAULT 0, job text DEFAULT 1,powerload integer,resend integer DEFAULT 0)");
        sQLiteDatabase.execSQL("create table setting (id integer primary key AUTOINCREMENT, type text,sim text,pos integer)");
        sQLiteDatabase.execSQL("create table income_message (id integer primary key AUTOINCREMENT, body text,sender text,delivery text)");
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public void onUpgrade(SQLiteDatabase sQLiteDatabase, int i, int i2) {
        sQLiteDatabase.execSQL("DROP TABLE IF EXISTS income_log");
        sQLiteDatabase.execSQL("DROP TABLE IF EXISTS setting");
        sQLiteDatabase.execSQL("DROP TABLE IF EXISTS income_message");
        onCreate(sQLiteDatabase);
    }

    public boolean check_data_sttings(String str) {
        SQLiteDatabase readableDatabase = getReadableDatabase();
        StringBuilder sb = new StringBuilder();
        sb.append("select * from setting where sim=\"");
        sb.append(str);
        sb.append("\"");
        return readableDatabase.rawQuery(sb.toString(), null).getCount() == 0;
    }

    public boolean insertsetting(String str, String str2, int i) {
        SQLiteDatabase writableDatabase = getWritableDatabase();
        ContentValues contentValues = new ContentValues();
        contentValues.put("sim", str);
        contentValues.put("type", str2);
        contentValues.put("pos", Integer.valueOf(i));
        writableDatabase.insert(Seting_TABLE_NAME, null, contentValues);
        return true;
    }

    public Cursor getstting(String str, String str2) {
        return getReadableDatabase().rawQuery("select * from setting where type=" + str + " and sim=" + str2 + "", null);
    }

    public boolean insertSMS(String str, String str2, String str3) {
        SQLiteDatabase writableDatabase = getWritableDatabase();
        ContentValues contentValues = new ContentValues();
        contentValues.put("body", str);
        contentValues.put("sender", str2);
        contentValues.put("delivery", str3);
        writableDatabase.insert("income_message", null, contentValues);
        return true;
    }

    public boolean already(String str) {
        Cursor rawQuery = getWritableDatabase().rawQuery("Select * from income_log where orderid = " + str, null);
        if (rawQuery.getCount() <= 0) {
            rawQuery.close();
            return false;
        }
        rawQuery.close();
        return true;
    }

    public boolean insertContact(String str, String str2, String str3, String str4, String str5, int i, String str6, String str7, String str8, String str9, String str10, int i2, int i3, int i4, int i5, int i6) {
        SQLiteDatabase writableDatabase = getWritableDatabase();
        ContentValues contentValues = new ContentValues();
        contentValues.put("orderid", str);
        contentValues.put("resend", Integer.valueOf(i6));
        contentValues.put(CONTACTS_COLUMN_NUMBER, str2);
        contentValues.put(CONTACTS_COLUMN_AMOUNT, str3);
        contentValues.put("status", Integer.valueOf(i5));
        if (str4 != null && !str4.isEmpty()) {
            contentValues.put("ussd", str4);
        }
        if (str5 != null && !str5.isEmpty()) {
            contentValues.put("pcode", str5);
        }
        contentValues.put("line", Integer.valueOf(i));
        contentValues.put("powerload", Integer.valueOf(i4));
        if (str6 != null && !str6.isEmpty()) {
            contentValues.put("one", str6);
        }
        if (str7 != null && !str7.isEmpty()) {
            contentValues.put("two", str7);
        }
        if (str8 != null && !str8.isEmpty()) {
            contentValues.put("three", str8);
        }
        if (str9 != null && !str9.isEmpty()) {
            contentValues.put("four", str9);
        }
        if (str10 != null && !str10.isEmpty()) {
            contentValues.put("five", str10);
        }
        contentValues.put("triger", Integer.valueOf(i2));
        contentValues.put("slot", Integer.valueOf(i3));
        if (!already(str)) {
            writableDatabase.insert(CONTACTS_TABLE_NAME, null, contentValues);
            Intent intent = new Intent("BackgroundService");
            intent.putExtra(CONTACTS_COLUMN_ID, str);
            intent.putExtra("act", "in");
            LocalBroadcastManager.getInstance(this.appContext).sendBroadcast(intent);
        } else {
            writableDatabase.update(CONTACTS_TABLE_NAME, contentValues, "orderid = ? ", new String[]{str});
            Intent intent2 = new Intent("BackgroundService");
            intent2.putExtra(CONTACTS_COLUMN_ID, str);
            intent2.putExtra("act", "up");
            LocalBroadcastManager.getInstance(this.appContext).sendBroadcast(intent2);
        }
        return true;
    }

    public Cursor getallData() {
        Cursor rawQuery = getReadableDatabase().rawQuery("select * from income_log", null);
        if (rawQuery != null) {
            rawQuery.moveToFirst();
        }
        return rawQuery;
    }

    public Cursor getData(String str) {
        Cursor rawQuery = getWritableDatabase().rawQuery("select * from income_log where orderid=" + str + "", null);
        if (rawQuery != null) {
            rawQuery.moveToFirst();
        }
        return rawQuery;
    }

    public Cursor getDatamain(String str) {
        Cursor rawQuery = getWritableDatabase().rawQuery("select * from income_log where id=" + str + "", null);
        if (rawQuery != null) {
            rawQuery.moveToFirst();
        }
        return rawQuery;
    }

    public boolean check_data(String str, String str2) {
        SQLiteDatabase readableDatabase = getReadableDatabase();
        StringBuilder sb = new StringBuilder();
        sb.append("select * from income_log where time >= datetime('now', '-2 minutes') AND number=\"");
        sb.append(str);
        sb.append("\" AND amount=\"");
        sb.append(str2);
        sb.append("\"");
        return readableDatabase.rawQuery(sb.toString(), null).getCount() == 0;
    }

    public boolean check_data_resend(String str, String str2) {
        SQLiteDatabase readableDatabase = getReadableDatabase();
        StringBuilder sb = new StringBuilder();
        sb.append("select * from income_log where time >= datetime('now', '-1 minutes') AND number=\"");
        sb.append(str);
        sb.append("\" AND amount=\"");
        sb.append(str2);
        sb.append("\"");
        return readableDatabase.rawQuery(sb.toString(), null).getCount() == 0;
    }

    public int resend(String str) {
        int i;
        Cursor cursor = null;
        try {
            cursor = getWritableDatabase().rawQuery("select * from income_log where  orderid=\"" + str + "\"", null);
            if (cursor.getCount() > 0) {
                cursor.moveToFirst();
                i = cursor.getInt(cursor.getColumnIndex("resend"));
            } else {
                i = 0;
            }
            return i;
        } finally {
            cursor.close();
        }
    }

    public String getEmployeeName(String str, String str2) {
        String str3;
        String str4;
        Cursor cursor = null;
        try {
            cursor = getWritableDatabase().rawQuery("select * from income_log where time >= datetime('now', '-10 minutes') AND number=\"" + str + "\" AND amount=\"" + str2 + "\"", null);
            String str5 = "";
            if (cursor.getCount() > 0) {
                cursor.moveToFirst();
                str5 = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_NUMBER));
                str3 = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_AMOUNT));
                str4 = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_TIME));
            } else {
                str3 = "";
                str4 = str3;
            }
            return str5 + str3 + str4;
        } finally {
            cursor.close();
        }
    }

    public Cursor fetch() {
        SQLiteDatabase sQLiteDatabase;
        try {
            sQLiteDatabase = getWritableDatabase();
        } catch (SQLException unused) {
            new Exception("Error with DB Open");
            sQLiteDatabase = null;
        }
        Cursor query = sQLiteDatabase.query("income_message", new String[]{CONTACTS_COLUMN_ID, "body", "sender"}, null, null, null, null, "Id asc");
        if (query != null) {
            query.moveToFirst();
        }
        sQLiteDatabase.close();
        return query;
    }

    public Cursor fetchlast() {
        Cursor query = getWritableDatabase().query(CONTACTS_TABLE_NAME, new String[]{CONTACTS_COLUMN_ID, "orderid", CONTACTS_COLUMN_AMOUNT, "ussd", "slot"}, "status=0", null, null, null, "Id asc");
        if (query != null) {
            query.moveToFirst();
        }
        return query;
    }

    public Cursor fetchPendingRecharges() {
        Cursor query = getWritableDatabase().query(CONTACTS_TABLE_NAME, new String[]{CONTACTS_COLUMN_ID, "orderid", CONTACTS_COLUMN_AMOUNT, "ussd", "slot"}, "status=0", null, null, null, "Id asc");
        if (query != null) {
            query.moveToFirst();
        }
        return query;
    }

    public Cursor fetchresponsenew() {
        Cursor query = getWritableDatabase().query(CONTACTS_TABLE_NAME, new String[]{CONTACTS_COLUMN_ID, "orderid", "apiresponse"}, "ostatus=0 and apiresponse IS NOT NULL", null, null, null, "ID ASC limit 1");
        if (query != null) {
            query.moveToFirst();
        }
        return query;
    }

    public void removeSingleContactx() {
        SQLiteDatabase writableDatabase = getWritableDatabase();
        try {
            if (writableDatabase.rawQuery("select * from income_log where status!=0", null).getCount() > 40) {
                writableDatabase.execSQL("DELETE FROM income_log WHERE status!=0 AND id in (select id from income_log order by id desc limit 5)");
            }
        } finally {
            writableDatabase.close();
        }
    }

    public void deletetex() {
        Cursor cursor = null;
        try {
            cursor = getWritableDatabase().query("delete from income_log", null, null, null, null, null, "ID DESC limit 5");
            if (cursor != null) {
                cursor.moveToFirst();
            }
        } finally {
            cursor.close();
        }
    }

    public Cursor responsequery(String str) {
        Cursor query = getWritableDatabase().query(CONTACTS_TABLE_NAME, new String[]{CONTACTS_COLUMN_ID, "orderid", "apiresponse"}, "orderid=" + str + "", null, null, null, "ID ASC limit 1");
        if (query != null) {
            query.moveToFirst();
        }
        return query;
    }

    public int order(String str) {
        Cursor cursor = null;
        try {
            cursor = getWritableDatabase().rawQuery("SELECT * FROM income_log WHERE status=?", new String[]{str + ""});
            if (cursor.getCount() > 0) {
                cursor.moveToFirst();
                this.number = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_NUMBER));
                this.operator = cursor.getString(cursor.getColumnIndex("operator"));
                this.amount = cursor.getString(cursor.getColumnIndex(CONTACTS_COLUMN_AMOUNT));
                this.extra = cursor.getString(cursor.getColumnIndex("extra"));
                this.id = cursor.getInt(cursor.getColumnIndex(CONTACTS_COLUMN_ID));
                updateQty("1", String.valueOf(this.id));
                Intent intent = new Intent(this.appContext, (Class<?>) Recharge.class);
                intent.putExtra(CONTACTS_COLUMN_NUMBER, this.number);
                intent.putExtra(CONTACTS_COLUMN_AMOUNT, this.amount);
                intent.putExtra("operator", this.operator);
                intent.putExtra("extra", this.extra);
                this.appContext.startService(intent);
            }
            return this.id;
        } finally {
            cursor.close();
        }
    }

    public void updateQty(String str, String str2) {
        try {
            SQLiteDatabase writableDatabase = getWritableDatabase();
            writableDatabase.execSQL("update income_log set status = " + str + " where " + CONTACTS_COLUMN_ID + " = '" + str2 + "';");
            writableDatabase.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public int numberOfRows() {
        return (int) DatabaseUtils.queryNumEntries(getReadableDatabase(), CONTACTS_TABLE_NAME);
    }

    public boolean updateo(Integer num, String str) {
        SQLiteDatabase writableDatabase = getWritableDatabase();
        ContentValues contentValues = new ContentValues();
        contentValues.put("status", str);
        writableDatabase.update(CONTACTS_TABLE_NAME, contentValues, "id = ? ", new String[]{Integer.toString(num.intValue())});
        return true;
    }

    public boolean updateContact(Integer num, String str, String str2, String str3) {
        SQLiteDatabase writableDatabase = getWritableDatabase();
        ContentValues contentValues = new ContentValues();
        contentValues.put("body", str);
        contentValues.put("sender", str2);
        contentValues.put("delivery", str2);
        writableDatabase.update("income_message", contentValues, "id = ? ", new String[]{Integer.toString(num.intValue())});
        return true;
    }

    public boolean updateinfo(Integer num, String str, String str2) {
        SQLiteDatabase writableDatabase = getWritableDatabase();
        ContentValues contentValues = new ContentValues();
        contentValues.put(str, str2);
        writableDatabase.update(CONTACTS_TABLE_NAME, contentValues, "id = ? ", new String[]{Integer.toString(num.intValue())});
        Intent intent = new Intent("BackgroundService");
        intent.putExtra(CONTACTS_COLUMN_ID, "1");
        intent.putExtra("act", "up");
        LocalBroadcastManager.getInstance(this.appContext).sendBroadcast(intent);
        return true;
    }

    public boolean updateresend(String str, String str2, String str3, int i, int i2) {
        SQLiteDatabase writableDatabase = getWritableDatabase();
        ContentValues contentValues = new ContentValues();
        contentValues.put(str2, str3);
        contentValues.put("status", "0");
        contentValues.put("resend", Integer.valueOf(i));
        contentValues.put("apiresponse", "");
        contentValues.put("job", "1");
        contentValues.put("line", Integer.valueOf(i2));
        writableDatabase.update(CONTACTS_TABLE_NAME, contentValues, "orderid = ? ", new String[]{str});
        Intent intent = new Intent("BackgroundService");
        intent.putExtra(CONTACTS_COLUMN_ID, str);
        intent.putExtra("act", "up");
        LocalBroadcastManager.getInstance(this.appContext).sendBroadcast(intent);
        return true;
    }

    public boolean updateSetting(String str, String str2, int i) {
        SQLiteDatabase writableDatabase = getWritableDatabase();
        ContentValues contentValues = new ContentValues();
        contentValues.put("type", str2);
        contentValues.put("pos", Integer.valueOf(i));
        writableDatabase.update(Seting_TABLE_NAME, contentValues, "sim = ? ", new String[]{str});
        return true;
    }

    public boolean updateresponse(String str, String str2) {
        SQLiteDatabase writableDatabase = getWritableDatabase();
        ContentValues contentValues = new ContentValues();
        contentValues.put("apiresponse", str2);
        contentValues.put("ostatus", "0");
        writableDatabase.update(CONTACTS_TABLE_NAME, contentValues, "orderid = ? ", new String[]{str});
        return true;
    }

    public boolean remresponse(String str) {
        SQLiteDatabase writableDatabase = getWritableDatabase();
        ContentValues contentValues = new ContentValues();
        contentValues.put("ostatus", "1");
        writableDatabase.update(CONTACTS_TABLE_NAME, contentValues, "orderid = ? ", new String[]{str});
        return true;
    }

    public Integer deletesms(Integer num) {
        return Integer.valueOf(getWritableDatabase().delete("income_message", "id = ? ", new String[]{Integer.toString(num.intValue())}));
    }

    public int getstingpo(String str) {
        int i;
        Cursor cursor = null;
        try {
            cursor = getWritableDatabase().rawQuery("select * from setting where sim=\"" + str + "\"", null);
            if (cursor.getCount() > 0) {
                cursor.moveToFirst();
                i = cursor.getInt(cursor.getColumnIndex("pos"));
            } else {
                i = 0;
            }
            return i;
        } finally {
            cursor.close();
        }
    }

    public int getsim(String str) {
        int i;
        Cursor cursor = null;
        try {
            cursor = getWritableDatabase().rawQuery("select * from setting where type=\"" + str + "\"", null);
            if (cursor.getCount() > 0) {
                cursor.moveToFirst();
                i = cursor.getInt(cursor.getColumnIndex("sim"));
            } else {
                i = 0;
            }
            return i;
        } finally {
            cursor.close();
        }
    }

    public String getAndroidVersion() {
        String str = Build.VERSION.RELEASE;
        int i = Build.VERSION.SDK_INT;
        return str;
    }

    private boolean isOnline(Context context) {
        NetworkInfo activeNetworkInfo = ((ConnectivityManager) context.getSystemService("connectivity")).getActiveNetworkInfo();
        return activeNetworkInfo != null && activeNetworkInfo.isConnectedOrConnecting();
    }

    public String getDeviceName() {
        String str = Build.MANUFACTURER;
        return capitalize(Build.MODEL);
    }

    private String capitalize(String str) {
        if (str == null || str.length() == 0) {
            return "";
        }
        char charAt = str.charAt(0);
        if (Character.isUpperCase(charAt)) {
            return str;
        }
        return Character.toUpperCase(charAt) + str.substring(1);
    }

    public static String getPref(String str, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, null);
    }

    public void SavePreferences(String str, String str2) {
        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(this.appContext).edit();
        edit.putString(str, str2);
        edit.commit();
    }

    /**
     * Update recharge status in database
     */
    public boolean updateRechargeStatus(String orderId, String status, String response) {
        try {
            SQLiteDatabase db = getWritableDatabase();
            ContentValues values = new ContentValues();
            values.put("status", status);
            if (response != null) {
                values.put("apiresponse", response);
            }
            values.put("ostatus", "1"); // Mark as processed

            int rowsAffected = db.update("income_log", values, "orderid = ?", new String[]{orderId});
            db.close();

            Log.d("DbHelper", "Updated recharge status for order: " + orderId + ", rows affected: " + rowsAffected);
            return rowsAffected > 0;
        } catch (Exception e) {
            Log.e("DbHelper", "Error updating recharge status", e);
            return false;
        }
    }

    /**
     * Mark response record as processed
     */
    public void markResponseAsProcessed(String orderId) {
        try {
            SQLiteDatabase db = getWritableDatabase();
            ContentValues values = new ContentValues();
            values.put("ostatus", "1"); // Mark as processed
            db.update("income_log", values, "orderid = ?", new String[]{orderId});
            db.close();
            Log.d("DbHelper", "Marked response as processed for order: " + orderId);
        } catch (Exception e) {
            Log.e("DbHelper", "Error removing response", e);
        }
    }
}