package com.appystore.mrecharge.activity;


import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.appystore.mrecharge.R;
import java.io.IOException;
import java.io.LineNumberReader;
import java.io.StringReader;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Tes extends AppCompatActivity {
    private static final String[] smsser = {"Defult", "Custom 1"};
    boolean flag;
    String job = null;
    boolean next = false;
    String s = null;
    String servname;

    /* access modifiers changed from: protected */
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView((int) R.layout.forward);
        try {
            String rcfind = rcfind("30D @598Tk (C: 90)\n4. Free 4G SIM at 63Tk Recharge \n5. Flash Drive 510mins, 30D@TK307 (C:45)\n p. Prev \n#. Next", "Recharge", 30);
            Context baseContext = getBaseContext();
            Toast.makeText(baseContext, "s--" + rcfind, Toast.LENGTH_LONG).show();
        } catch (IOException e) {
            e.printStackTrace();
        }
        this.flag = true;
        try {
            this.s = testLineExtract("30D @598Tk (C: 90)\n4. Free 4G SIM at 63Tk Recharge \n5. Flash Drive 510mins, 30D@TK307 (C:45)\n p. Prev \n#. Next", 30, 0);
            Log.d("good", "n" + this.s);
            if (!TextUtils.isEmpty(this.s)) {
                this.job = this.s;
            } else if ("30D @598Tk (C: 90)\n4. Free 4G SIM at 63Tk Recharge \n5. Flash Drive 510mins, 30D@TK307 (C:45)\n p. Prev \n#. Next".toLowerCase().indexOf("next") >= 0) {
                this.next = true;
                this.job = morefind("30D @598Tk (C: 90)\n4. Free 4G SIM at 63Tk Recharge \n5. Flash Drive 510mins, 30D@TK307 (C:45)\n p. Prev \n#. Next", "next");
            } else {
                this.job = deffind("30D @598Tk (C: 90)\n4. Free 4G SIM at 63Tk Recharge \n5. Flash Drive 510mins, 30D@TK307 (C:45)\n p. Prev \n#. Next", "Default", 30);
                if (TextUtils.isEmpty(this.job)) {
                    this.job = deffind("30D @598Tk (C: 90)\n4. Free 4G SIM at 63Tk Recharge \n5. Flash Drive 510mins, 30D@TK307 (C:45)\n p. Prev \n#. Next", "Main", 30);
                }
                if (TextUtils.isEmpty(this.job)) {
                    this.job = rcfind("30D @598Tk (C: 90)\n4. Free 4G SIM at 63Tk Recharge \n5. Flash Drive 510mins, 30D@TK307 (C:45)\n p. Prev \n#. Next", "Recharge", 30);
                }
            }
        } catch (IOException e2) {
            e2.printStackTrace();
        }
        if (haveblank(this.job)) {
            this.job = "1";
        }
        if (TextUtils.isEmpty(this.job)) {
            Context baseContext2 = getBaseContext();
            Toast.makeText(baseContext2, "empty--" + this.job, Toast.LENGTH_LONG).show();
        } else {
            Context baseContext3 = getBaseContext();
            Toast.makeText(baseContext3, "ok--" + this.job, Toast.LENGTH_LONG).show();
        }
        if (TextUtils.isEmpty(this.job)) {
            Log.d("good", "no");
            return;
        }
        Log.d("good", "job-" + this.job);
    }

    private String capitalize(String str) {
        return (str == null || str.length() == 0) ? "" : str.toLowerCase();
    }

    private String testLineExtract(String str, int i, int i2) throws IOException {
        LineNumberReader lineNumberReader = new LineNumberReader(new StringReader(capitalize(str)));
        while (true) {
            String readLine = lineNumberReader.readLine();
            if (readLine == null) {
                break;
            } else if (lineNumberReader.getLineNumber() != i2) {
                Log.d("oi", "o-" + readLine);
                System.out.println("Could not parse " + lineNumberReader.getLineNumber());
                Matcher matcher = Pattern.compile("-?\\d+").matcher(readLine);
                while (matcher.find()) {
                    int intValue = new Integer(matcher.group().toString()).intValue();
                    if (intValue == i) {
                        String str2 = intValue + "tk";
                        String str3 = "tk" + intValue;
                        String tkfindfinall = tkfindfinall(readLine.toLowerCase());
                        if ((tkfindfinall.equals(str2) || tkfindfinall.equals(str3)) && TextUtils.isEmpty(deffind(readLine, "Main", i)) && TextUtils.isEmpty(deffind(readLine, "Default", i))) {
                            String[] split = readLine.split("\\s+");
                            System.out.println("goo " + readLine.toLowerCase().indexOf("main"));
                            int i3 = 0;
                            String replaceAll = split[0].replaceAll("\\D+", "");
                            try {
                                i3 = Integer.parseInt(replaceAll);
                            } catch (NumberFormatException e) {
                                System.out.println("Could not " + e);
                            }
                            if (i3 == i) {
                                return null;
                            }
                            return replaceAll;
                        }
                    }
                }
                continue;
            }
        }
        return null;
    }

    /* JADX WARNING: Removed duplicated region for block: B:25:0x0063 A[SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    private java.lang.String textfromdefult(java.lang.String r12, int r13, java.lang.String r14) throws java.io.IOException {
        /*
            r11 = this;
            java.lang.String r12 = r11.capitalize(r12)
            java.lang.String r0 = "[-\\[\\]^/,':.!><~@$%+=?|\"\\\\()]+"
            java.lang.String r1 = " "
            java.lang.String r12 = r12.replaceAll(r0, r1)
            java.io.LineNumberReader r0 = new java.io.LineNumberReader
            java.io.StringReader r1 = new java.io.StringReader
            r1.<init>(r12)
            r0.<init>(r1)
        L_0x0016:
            java.lang.String r12 = r0.readLine()
            r1 = 0
            if (r12 == 0) goto L_0x00b6
            java.lang.String r2 = "-?\\d+"
            java.util.regex.Pattern r3 = java.util.regex.Pattern.compile(r2)
            java.util.regex.Matcher r3 = r3.matcher(r12)
        L_0x0027:
            boolean r4 = r3.find()
            if (r4 == 0) goto L_0x0016
            java.lang.String r4 = "\\s+"
            java.lang.String[] r4 = r12.split(r4)
            r5 = 0
            r4 = r4[r5]
            java.lang.String r6 = ""
            java.lang.String r7 = "\\D+"
            java.lang.String r4 = r4.replaceAll(r7, r6)
            int r4 = java.lang.Integer.parseInt(r4)     // Catch:{ NumberFormatException -> 0x0049 }
            int r5 = java.lang.Integer.parseInt(r14)     // Catch:{ NumberFormatException -> 0x0047 }
            goto L_0x0061
        L_0x0047:
            r7 = move-exception
            goto L_0x004b
        L_0x0049:
            r7 = move-exception
            r4 = 0
        L_0x004b:
            java.io.PrintStream r8 = java.lang.System.out
            java.lang.StringBuilder r9 = new java.lang.StringBuilder
            r9.<init>()
            java.lang.String r10 = "Could not parse "
            r9.append(r10)
            r9.append(r7)
            java.lang.String r7 = r9.toString()
            r8.println(r7)
        L_0x0061:
            if (r5 != r4) goto L_0x0027
            java.util.regex.Pattern r14 = java.util.regex.Pattern.compile(r2)
            java.util.regex.Matcher r14 = r14.matcher(r12)
        L_0x006b:
            boolean r0 = r14.find()
            if (r0 == 0) goto L_0x00b6
            java.lang.Integer r0 = new java.lang.Integer
            java.lang.String r2 = r14.group()
            java.lang.String r2 = r2.toString()
            r0.<init>(r2)
            int r0 = r0.intValue()
            if (r0 != r13) goto L_0x006b
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            r2.append(r6)
            r2.append(r0)
            java.lang.String r2 = r2.toString()
            java.lang.String r3 = "good"
            android.util.Log.d(r3, r2)
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            r2.append(r0)
            java.lang.String r0 = "tk"
            r2.append(r0)
            java.lang.String r0 = r2.toString()
            java.lang.String r2 = r12.toLowerCase()
            int r0 = r2.indexOf(r0)
            if (r0 < 0) goto L_0x006b
            java.lang.String r1 = "ok"
            goto L_0x006b
        L_0x00b6:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: com.flexisoftwarebd.serverpro.activity.Tes.textfromdefult(java.lang.String, int, java.lang.String):java.lang.String");
    }

    private String rcfind(String str, String str2, int i) throws IOException {
        String capitalize = capitalize(str);
        String capitalize2 = capitalize(str2);
        LineNumberReader lineNumberReader = new LineNumberReader(new StringReader(capitalize));
        while (true) {
            String readLine = lineNumberReader.readLine();
            if (readLine == null) {
                return null;
            }
            if (capitalize(readLine).indexOf(capitalize2) >= 0) {
                Matcher matcher = Pattern.compile("-?\\d+").matcher(readLine);
                while (matcher.find()) {
                    int intValue = new Integer(matcher.group().toString()).intValue();
                    if (intValue == i) {
                        if (readLine.toLowerCase().indexOf("tk " + intValue) >= 0) {
                            return readLine.split("\\s+")[0].replaceAll("\\.", "").replaceAll("\\s+", "");
                        }
                    }
                }
                continue;
            }
        }
    }

    private String deffind(String str, String str2, int i) throws IOException {
        String capitalize = capitalize(str);
        String capitalize2 = capitalize(str2);
        LineNumberReader lineNumberReader = new LineNumberReader(new StringReader(capitalize));
        while (true) {
            String readLine = lineNumberReader.readLine();
            if (readLine == null) {
                return null;
            }
            if (capitalize(readLine).indexOf(capitalize2) >= 0) {
                Matcher matcher = Pattern.compile("-?\\d+").matcher(readLine);
                while (matcher.find()) {
                    int intValue = new Integer(matcher.group().toString()).intValue();
                    if (intValue == i) {
                        if (readLine.toLowerCase().indexOf(intValue + "tk") >= 0) {
                            return readLine.split("\\s+")[0].replaceAll("\\.", "").replaceAll("\\s+", "");
                        }
                    }
                }
                continue;
            }
        }
    }

    private String tkfind(String str) {
        Matcher matcher = Pattern.compile("(tk)(\\d+)").matcher(str);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    private String tkfind2(String str) {
        Matcher matcher = Pattern.compile("(\\d+)(tk)").matcher(str);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    private String tkfindfinall(String str) {
        String str2;
        if (!TextUtils.isEmpty(tkfind(str))) {
            str2 = tkfind(str);
        } else {
            str2 = tkfind2(str);
        }
        return !TextUtils.isEmpty(str2) ? str2.toLowerCase() : "osman";
    }

    private String morefind(String str, String str2) throws IOException {
        LineNumberReader lineNumberReader = new LineNumberReader(new StringReader(capitalize(str)));
        String str3 = null;
        while (true) {
            String readLine = lineNumberReader.readLine();
            if (readLine == null) {
                return str3;
            }
            String capitalize = capitalize(readLine);
            Log.d("good", "" + capitalize);
            if (capitalize.equals("*. back00. next")) {
                str3 = "00";
            } else if (capitalize.indexOf(str2) >= 0) {
                return readLine.split("\\s+")[0].replaceAll("\\.", "").replaceAll("\\s+", "");
            }
        }
    }

    public boolean haveblank(String str) {
        return TextUtils.isEmpty(str);
    }

    public void SavePreferences(String str, String str2) {
        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit();
        edit.putString(str, str2);
        edit.commit();
    }
}