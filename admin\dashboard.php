<?php
/**
 * Admin Panel - Dashboard
 *
 * This file displays the main dashboard for the admin panel.
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include configuration
require_once 'config.php';

// Check if user is logged in
if (!checkAdminSession()) {
    header('Location: login.php');
    exit;
}

// Handle logout
if (isset($_GET['logout']) && $_GET['logout'] === '1') {
    destroyAdminSession();
    header('Location: login.php');
    exit;
}

// Handle change password
if (isset($_POST['action']) && $_POST['action'] === 'change_password') {
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $message = "All password fields are required.";
        $message_type = "danger";
    } elseif ($new_password !== $confirm_password) {
        $message = "New password and confirmation do not match.";
        $message_type = "danger";
    } else {
        $result = changeUserPassword($_SESSION['admin_user_id'], $current_password, $new_password);
        $message = $result['message'];
        $message_type = $result['success'] ? 'success' : 'danger';
    }
}

// Get database connection
$conn = getDbConnection();
$db_error = null;

// Handle actions (clear logs or approve/reject requests)
$message = '';
$message_type = '';

// Handle approval/rejection
if (isset($_POST['action']) && ($_POST['action'] === 'approve' || $_POST['action'] === 'reject')) {
    if (!isset($db_error)) {
        $log_id = isset($_POST['log_id']) ? (int)$_POST['log_id'] : 0;
        $approval_status = $_POST['action'] === 'approve' ? 'approved' : 'rejected';

        if ($log_id > 0) {
            $sql = "UPDATE auth_logs SET approval_status = ?, approved_at = NOW() WHERE id = ?";
            $stmt = $conn->prepare($sql);

            if ($stmt) {
                $stmt->bind_param("si", $approval_status, $log_id);

                if ($stmt->execute()) {
                    $message = "Authentication request has been " . $approval_status . ".";
                    $message_type = "success";
                } else {
                    $message = "Error updating approval status: " . $stmt->error;
                    $message_type = "danger";
                }

                $stmt->close();
            } else {
                $message = "Error preparing statement: " . $conn->error;
                $message_type = "danger";
            }
        } else {
            $message = "Invalid request ID.";
            $message_type = "danger";
        }
    }
}

// Handle clear logs
if (isset($_POST['action']) && $_POST['action'] === 'clear_logs') {
    if (!isset($db_error)) {
        // Check if date range is provided
        $start_date = isset($_POST['clear_start_date']) && !empty($_POST['clear_start_date']) ? $_POST['clear_start_date'] : null;
        $end_date = isset($_POST['clear_end_date']) && !empty($_POST['clear_end_date']) ? $_POST['clear_end_date'] : null;

        // Check what type of logs to clear
        $clear_type = isset($_POST['clear_type']) ? $_POST['clear_type'] : 'failed_only';

        // Build the SQL query based on clear type
        $sql = "DELETE FROM auth_logs";
        $params = [];
        $types = "";
        $conditions = [];

        // Add condition based on clear type
        if ($clear_type === 'failed_only') {
            // Only clear failed authentication attempts, preserve device history (successful logins)
            $conditions[] = "status != 'success'";
        } elseif ($clear_type === 'pending_only') {
            // Only clear pending approval requests
            $conditions[] = "approval_status = 'pending'";
        } elseif ($clear_type === 'rejected_only') {
            // Only clear rejected requests
            $conditions[] = "approval_status = 'rejected'";
        } elseif ($clear_type === 'all_except_success') {
            // Clear all except successful device authentications (preserve device history)
            $conditions[] = "(status != 'success' OR approval_status != 'approved')";
        }
        // If clear_type is 'all', no additional conditions (clears everything)

        // Add date range condition if provided
        if ($start_date && $end_date) {
            $end_date = date('Y-m-d', strtotime($end_date . ' +1 day')); // Include the end date
            $conditions[] = "created_at BETWEEN ? AND ?";
            $params = [$start_date, $end_date];
            $types = "ss";
        }

        // Add WHERE clause if there are conditions
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        // Prepare and execute the query
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            if (!empty($params)) {
                $stmt->bind_param($types, ...$params);
            }

            if ($stmt->execute()) {
                $affected_rows = $stmt->affected_rows;
                $clear_type_text = '';
                switch ($clear_type) {
                    case 'failed_only':
                        $clear_type_text = ' failed authentication';
                        break;
                    case 'pending_only':
                        $clear_type_text = ' pending approval';
                        break;
                    case 'rejected_only':
                        $clear_type_text = ' rejected';
                        break;
                    case 'all_except_success':
                        $clear_type_text = ' non-successful';
                        break;
                    case 'all':
                        $clear_type_text = '';
                        break;
                }
                $message = "Successfully cleared $affected_rows{$clear_type_text} log entries.";
                if ($clear_type !== 'all') {
                    $message .= " Device history (successful authentications) has been preserved.";
                }
                $message_type = "success";
            } else {
                $message = "Error clearing logs: " . $stmt->error;
                $message_type = "danger";
            }

            $stmt->close();
        } else {
            $message = "Error preparing statement: " . $conn->error;
            $message_type = "danger";
        }
    }
}

// Get statistics
$total_licenses = 0;
$active_devices = 0;
$recent_logins = [];
$total_logs = 0;

// If database connection is successful, get statistics
if (!isset($db_error)) {
    // Create auth_logs table if it doesn't exist
    $create_table_sql = "CREATE TABLE IF NOT EXISTS auth_logs (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        license_key VARCHAR(255) NOT NULL,
        pin VARCHAR(50) NOT NULL,
        device_id VARCHAR(255) NOT NULL,
        device_info TEXT,
        type VARCHAR(50),
        package VARCHAR(50),
        version VARCHAR(20),
        status VARCHAR(20),
        approval_status VARCHAR(20) DEFAULT 'pending',
        approved_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    // Check if approval_status column exists, if not add it
    $conn->query($create_table_sql);
    $check_column = $conn->query("SHOW COLUMNS FROM auth_logs LIKE 'approval_status'");
    if ($check_column->num_rows === 0) {
        $conn->query("ALTER TABLE auth_logs ADD COLUMN approval_status VARCHAR(20) DEFAULT 'pending' AFTER status");
    }

    // Check if approved_at column exists, if not add it
    $check_column = $conn->query("SHOW COLUMNS FROM auth_logs LIKE 'approved_at'");
    if ($check_column->num_rows === 0) {
        $conn->query("ALTER TABLE auth_logs ADD COLUMN approved_at TIMESTAMP NULL AFTER approval_status");
    }

    // Get total number of unique license keys
    $result = $conn->query("SELECT COUNT(DISTINCT license_key) as total FROM auth_logs");
    if ($result && $row = $result->fetch_assoc()) {
        $total_licenses = $row['total'];
    }

    // Get total number of unique device IDs
    $result = $conn->query("SELECT COUNT(DISTINCT device_id) as total FROM auth_logs");
    if ($result && $row = $result->fetch_assoc()) {
        $active_devices = $row['total'];
    }

    // Pagination settings
    $items_per_page = 10;
    $current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $offset = ($current_page - 1) * $items_per_page;

    // Filter settings
    $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
    $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';
    $status_filter = isset($_GET['status']) ? $_GET['status'] : '';
    $approval_status_filter = isset($_GET['approval_status']) ? $_GET['approval_status'] : '';

    // Check if we should add test data
    if (isset($_GET['add_test_data'])) {
        // Insert a test record
        $test_sql = "INSERT INTO auth_logs
                    (license_key, pin, device_id, device_info, type, package, version, status, approval_status)
                    VALUES
                    ('TEST-LICENSE', '1234', 'TEST-DEVICE-ID', 'Test Device Info', 'android', 'com.appystore.mrecharge', '1.0', 'success', 'approved')";

        if ($conn->query($test_sql)) {
            $message = "Added a test record successfully.";
            $message_type = "success";
            // Redirect to remove the parameter
            header("Location: dashboard.php");
            exit;
        } else {
            $message = "Failed to add test record: " . $conn->error;
            $message_type = "danger";
        }
    }

    // Get count of pending approvals
    $pending_count = 0;
    $result = $conn->query("SELECT COUNT(*) as count FROM auth_logs WHERE approval_status = 'pending'");
    if ($result && $row = $result->fetch_assoc()) {
        $pending_count = $row['count'];
    }

    // Build the query
    $sql = "SELECT * FROM auth_logs";
    $count_sql = "SELECT COUNT(*) as total FROM auth_logs";
    $where_conditions = [];
    $params = [];
    $types = "";

    // Add date range filter
    if (!empty($start_date) && !empty($end_date)) {
        $where_conditions[] = "created_at BETWEEN ? AND ?";
        $end_date_adjusted = date('Y-m-d', strtotime($end_date . ' +1 day')); // Include the end date
        $params[] = $start_date;
        $params[] = $end_date_adjusted;
        $types .= "ss";
    } else if (!empty($start_date)) {
        $where_conditions[] = "created_at >= ?";
        $params[] = $start_date;
        $types .= "s";
    } else if (!empty($end_date)) {
        $where_conditions[] = "created_at <= ?";
        $end_date_adjusted = date('Y-m-d', strtotime($end_date . ' +1 day')); // Include the end date
        $params[] = $end_date_adjusted;
        $types .= "s";
    }

    // Add status filter
    if (!empty($status_filter)) {
        $where_conditions[] = "status = ?";
        $params[] = $status_filter;
        $types .= "s";
    }

    // Add approval status filter
    if (!empty($approval_status_filter)) {
        $where_conditions[] = "approval_status = ?";
        $params[] = $approval_status_filter;
        $types .= "s";
    }

    // Combine where conditions
    if (!empty($where_conditions)) {
        $sql .= " WHERE " . implode(" AND ", $where_conditions);
        $count_sql .= " WHERE " . implode(" AND ", $where_conditions);
    }

    // Add order and limit
    $sql .= " ORDER BY created_at DESC LIMIT ?, ?";
    $params[] = $offset;
    $params[] = $items_per_page;
    $types .= "ii";

    // Get total count for pagination
    if (!empty($params) && !empty($types)) {
        // Remove the last two parameters (offset and limit) for the count query
        $count_params = array_slice($params, 0, -2);
        $count_types = substr($types, 0, -2);

        $stmt = $conn->prepare($count_sql);
        if ($stmt) {
            if (!empty($count_params)) {
                $stmt->bind_param($count_types, ...$count_params);
            }
            $stmt->execute();
            $result = $stmt->get_result();
            if ($row = $result->fetch_assoc()) {
                $total_logs = $row['total'];
            }
            $stmt->close();
        }
    } else {
        $result = $conn->query($count_sql);
        if ($result && $row = $result->fetch_assoc()) {
            $total_logs = $row['total'];
        }
    }

    // Get paginated results
    $stmt = $conn->prepare($sql);
    if ($stmt) {
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $recent_logins[] = $row;
        }
        $stmt->close();
    }

    // Calculate total pages
    $total_pages = ceil($total_logs / $items_per_page);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - AppyStore MRecharge Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/admin-style.css">
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: #f8f9fa;
        }
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .nav-link {
            font-weight: 500;
            color: #333;
        }
        .nav-link.active {
            color: #007bff;
        }
        main {
            padding-top: 48px;
        }
        .stat-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .filter-card {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .pagination {
            margin-top: 20px;
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.4em 0.6em;
        }
        .table-responsive {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .clear-logs-btn {
            transition: all 0.3s;
        }
        .clear-logs-btn:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#"><?php echo ADMIN_PANEL_TITLE; ?></a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="navbar-nav">
            <div class="nav-item dropdown text-nowrap">
                <a class="nav-link dropdown-toggle px-3" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-person-circle me-1"></i>
                    <?php echo htmlspecialchars($_SESSION['admin_full_name'] ?? $_SESSION['admin_username']); ?>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                    <li><h6 class="dropdown-header">
                        <i class="bi bi-person me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['admin_full_name']); ?>
                    </h6></li>
                    <li><small class="dropdown-item-text text-muted">
                        <i class="bi bi-envelope me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['admin_email']); ?>
                    </small></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                        <i class="bi bi-key me-2"></i>Change Password
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="?logout=1" onclick="return confirm('Are you sure you want to logout?')">
                        <i class="bi bi-box-arrow-right me-2"></i>Sign Out
                    </a></li>
                </ul>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3 sidebar-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="bi bi-speedometer2 me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="licenses.php">
                                <i class="bi bi-key me-2"></i>
                                Licenses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="devices.php">
                                <i class="bi bi-phone me-2"></i>
                                Devices
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="bi bi-gear me-2"></i>
                                Settings
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <hr class="text-muted">
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                <i class="bi bi-key-fill me-2"></i>
                                Change Password
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-danger" href="?logout=1" onclick="return confirm('Are you sure you want to logout?')">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="dashboard.php?add_test_data=1" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-plus-circle"></i> Add Test Data
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
                        </div>
                    </div>
                </div>

                <?php if (isset($db_error)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $db_error; ?>
                    </div>
                <?php endif; ?>

                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card stat-card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">Total Licenses</h5>
                                <h2 class="display-4"><?php echo $total_licenses; ?></h2>
                                <p class="card-text">Unique license keys in the system</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stat-card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">Active Devices</h5>
                                <h2 class="display-4"><?php echo $active_devices; ?></h2>
                                <p class="card-text">Devices currently registered</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stat-card bg-info text-white">
                            <div class="card-body">
                                <h5 class="card-title">Demo Licenses</h5>
                                <h2 class="display-4">∞</h2>
                                <p class="card-text">Unlimited demo licenses available</p>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?>" role="alert">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h2>Recent Authentication Attempts</h2>
                        <?php if ($pending_count > 0): ?>
                            <span class="badge bg-warning text-dark"><?php echo $pending_count; ?> pending approval<?php echo $pending_count > 1 ? 's' : ''; ?></span>
                        <?php endif; ?>
                    </div>

                    <div>
                        <!-- Pending Approvals Button -->
                        <?php if ($pending_count > 0): ?>
                            <a href="?approval_status=pending" class="btn btn-warning me-2">
                                <i class="bi bi-clock"></i> Pending Approvals (<?php echo $pending_count; ?>)
                            </a>
                        <?php endif; ?>

                        <!-- Clear Logs Button -->
                        <button type="button" class="btn btn-warning clear-logs-btn" data-bs-toggle="modal" data-bs-target="#clearLogsModal" title="Clear logs while preserving device history">
                            <i class="bi bi-funnel"></i> Clear Logs
                        </button>
                    </div>
                </div>

                <!-- Filter Form -->
                <div class="card mb-3 filter-card">
                    <div class="card-body">
                        <form method="get" action="dashboard.php" class="row g-3">
                            <div class="col-md-3">
                                <label for="start_date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="end_date" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All</option>
                                    <option value="success" <?php echo $status_filter === 'success' ? 'selected' : ''; ?>>Success</option>
                                    <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="approval_status" class="form-label">Approval Status</label>
                                <select class="form-select" id="approval_status" name="approval_status">
                                    <option value="">All</option>
                                    <option value="pending" <?php echo $approval_status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="approved" <?php echo $approval_status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                    <option value="rejected" <?php echo $approval_status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">Filter</button>
                                <a href="dashboard.php" class="btn btn-secondary">Reset</a>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th scope="col">ID</th>
                                <th scope="col">License Key</th>
                                <th scope="col">Device ID</th>
                                <th scope="col">Device Info</th>
                                <th scope="col">Status</th>
                                <th scope="col">Approval</th>
                                <th scope="col">Date</th>
                                <th scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($recent_logins)): ?>
                                <tr>
                                    <td colspan="8" class="text-center">No authentication attempts found</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($recent_logins as $login): ?>
                                    <tr <?php echo $login['approval_status'] === 'pending' ? 'class="table-warning"' : ''; ?>>
                                        <td><?php echo $login['id']; ?></td>
                                        <td><?php echo $login['license_key']; ?></td>
                                        <td><?php echo $login['device_id']; ?></td>
                                        <td><?php echo $login['device_info']; ?></td>
                                        <td>
                                            <?php if ($login['status'] === 'success'): ?>
                                                <span class="badge bg-success">Success</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Failed</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($login['approval_status'] === 'pending'): ?>
                                                <span class="badge bg-warning text-dark">Pending</span>
                                            <?php elseif ($login['approval_status'] === 'approved'): ?>
                                                <span class="badge bg-success">Approved</span>
                                            <?php elseif ($login['approval_status'] === 'rejected'): ?>
                                                <span class="badge bg-danger">Rejected</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $login['created_at']; ?></td>
                                        <td>
                                            <?php if ($login['approval_status'] === 'pending'): ?>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <form method="post" action="dashboard.php" class="d-inline">
                                                        <input type="hidden" name="action" value="approve">
                                                        <input type="hidden" name="log_id" value="<?php echo $login['id']; ?>">
                                                        <button type="submit" class="btn btn-success btn-sm" title="Approve">
                                                            <i class="bi bi-check-lg"></i>
                                                        </button>
                                                    </form>
                                                    <form method="post" action="dashboard.php" class="d-inline ms-1">
                                                        <input type="hidden" name="action" value="reject">
                                                        <input type="hidden" name="log_id" value="<?php echo $login['id']; ?>">
                                                        <button type="submit" class="btn btn-danger btn-sm" title="Reject">
                                                            <i class="bi bi-x-lg"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">—</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if (isset($total_pages) && $total_pages > 1): ?>
                    <nav aria-label="Authentication attempts pagination">
                        <ul class="pagination justify-content-center">
                            <!-- Previous page link -->
                            <li class="page-item <?php echo $current_page <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $current_page - 1; ?>&start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>&status=<?php echo $status_filter; ?>&approval_status=<?php echo $approval_status_filter; ?>" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>

                            <!-- Page number links -->
                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>&status=<?php echo $status_filter; ?>&approval_status=<?php echo $approval_status_filter; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <!-- Next page link -->
                            <li class="page-item <?php echo $current_page >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $current_page + 1; ?>&start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>&status=<?php echo $status_filter; ?>&approval_status=<?php echo $approval_status_filter; ?>" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                <?php endif; ?>

                <!-- Clear Logs Modal -->
                <div class="modal fade" id="clearLogsModal" tabindex="-1" aria-labelledby="clearLogsModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="clearLogsModalLabel">Clear Authentication Logs</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <form method="post" action="dashboard.php">
                                <div class="modal-body">
                                    <p class="text-warning"><i class="bi bi-exclamation-triangle"></i> <strong>Important:</strong> This action cannot be undone!</p>

                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i> <strong>Device History Protection:</strong>
                                        By default, device history (successful authentications) will be preserved to maintain device records.
                                    </div>

                                    <input type="hidden" name="action" value="clear_logs">

                                    <div class="mb-3">
                                        <label for="clear_type" class="form-label"><i class="bi bi-funnel"></i> What to Clear</label>
                                        <select class="form-select" id="clear_type" name="clear_type" onchange="updateClearDescription()">
                                            <option value="failed_only" selected>Failed Authentication Attempts Only</option>
                                            <option value="pending_only">Pending Approval Requests Only</option>
                                            <option value="rejected_only">Rejected Requests Only</option>
                                            <option value="all_except_success">All Non-Successful Logs (Preserve Device History)</option>
                                            <option value="all">⚠️ ALL LOGS (Including Device History)</option>
                                        </select>
                                        <div class="form-text" id="clear_description">
                                            Only failed authentication attempts will be cleared. Device history (successful logins) will be preserved.
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="clear_start_date" class="form-label"><i class="bi bi-calendar"></i> Start Date (Optional)</label>
                                        <input type="date" class="form-control" id="clear_start_date" name="clear_start_date">
                                    </div>

                                    <div class="mb-3">
                                        <label for="clear_end_date" class="form-label"><i class="bi bi-calendar"></i> End Date (Optional)</label>
                                        <input type="date" class="form-control" id="clear_end_date" name="clear_end_date">
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="submit" class="btn btn-danger" id="clear_logs_btn">Clear Selected Logs</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <script>
                function updateClearDescription() {
                    const clearType = document.getElementById('clear_type').value;
                    const description = document.getElementById('clear_description');
                    const button = document.getElementById('clear_logs_btn');

                    switch(clearType) {
                        case 'failed_only':
                            description.innerHTML = '<i class="bi bi-shield-check text-success"></i> Only failed authentication attempts will be cleared. Device history (successful logins) will be preserved.';
                            description.className = 'form-text text-success';
                            button.textContent = 'Clear Failed Attempts';
                            button.className = 'btn btn-warning';
                            break;
                        case 'pending_only':
                            description.innerHTML = '<i class="bi bi-shield-check text-success"></i> Only pending approval requests will be cleared. Device history will be preserved.';
                            description.className = 'form-text text-success';
                            button.textContent = 'Clear Pending Requests';
                            button.className = 'btn btn-warning';
                            break;
                        case 'rejected_only':
                            description.innerHTML = '<i class="bi bi-shield-check text-success"></i> Only rejected requests will be cleared. Device history will be preserved.';
                            description.className = 'form-text text-success';
                            button.textContent = 'Clear Rejected Requests';
                            button.className = 'btn btn-warning';
                            break;
                        case 'all_except_success':
                            description.innerHTML = '<i class="bi bi-shield-check text-success"></i> All non-successful logs will be cleared. Device history (successful authentications) will be preserved.';
                            description.className = 'form-text text-success';
                            button.textContent = 'Clear Non-Successful Logs';
                            button.className = 'btn btn-warning';
                            break;
                        case 'all':
                            description.innerHTML = '<i class="bi bi-exclamation-triangle text-danger"></i> <strong>WARNING:</strong> ALL logs including device history will be permanently deleted!';
                            description.className = 'form-text text-danger';
                            button.textContent = '⚠️ Clear ALL Logs';
                            button.className = 'btn btn-danger';
                            break;
                    }
                }
                </script>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Date filter validation
        document.addEventListener('DOMContentLoaded', function() {
            const startDateInput = document.getElementById('start_date');
            const endDateInput = document.getElementById('end_date');

            // Update end date min value when start date changes
            startDateInput.addEventListener('change', function() {
                if (startDateInput.value) {
                    endDateInput.min = startDateInput.value;

                    // If end date is before start date, update it
                    if (endDateInput.value && endDateInput.value < startDateInput.value) {
                        endDateInput.value = startDateInput.value;
                    }
                } else {
                    endDateInput.min = '';
                }
            });

            // Update start date max value when end date changes
            endDateInput.addEventListener('change', function() {
                if (endDateInput.value) {
                    startDateInput.max = endDateInput.value;

                    // If start date is after end date, update it
                    if (startDateInput.value && startDateInput.value > endDateInput.value) {
                        startDateInput.value = endDateInput.value;
                    }
                } else {
                    startDateInput.max = '';
                }
            });

            // Same for clear logs modal
            const clearStartDateInput = document.getElementById('clear_start_date');
            const clearEndDateInput = document.getElementById('clear_end_date');

            clearStartDateInput.addEventListener('change', function() {
                if (clearStartDateInput.value) {
                    clearEndDateInput.min = clearStartDateInput.value;

                    if (clearEndDateInput.value && clearEndDateInput.value < clearStartDateInput.value) {
                        clearEndDateInput.value = clearStartDateInput.value;
                    }
                } else {
                    clearEndDateInput.min = '';
                }
            });

            clearEndDateInput.addEventListener('change', function() {
                if (clearEndDateInput.value) {
                    clearStartDateInput.max = clearEndDateInput.value;

                    if (clearStartDateInput.value && clearStartDateInput.value > clearEndDateInput.value) {
                        clearStartDateInput.value = clearEndDateInput.value;
                    }
                } else {
                    clearStartDateInput.max = '';
                }
            });

            // Password confirmation validation
            const newPasswordInput = document.getElementById('new_password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const changePasswordBtn = document.getElementById('changePasswordBtn');

            function validatePasswords() {
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                if (confirmPassword && newPassword !== confirmPassword) {
                    confirmPasswordInput.classList.add('is-invalid');
                    changePasswordBtn.disabled = true;
                } else {
                    confirmPasswordInput.classList.remove('is-invalid');
                    changePasswordBtn.disabled = false;
                }
            }

            if (newPasswordInput && confirmPasswordInput) {
                newPasswordInput.addEventListener('input', validatePasswords);
                confirmPasswordInput.addEventListener('input', validatePasswords);
            }
        });
    </script>

    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="changePasswordModalLabel">
                        <i class="bi bi-key me-2"></i>Change Password
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="dashboard.php" id="changePasswordForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="change_password">

                        <div class="mb-3">
                            <label for="current_password" class="form-label">
                                <i class="bi bi-lock me-1"></i>Current Password
                            </label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">
                                <i class="bi bi-key me-1"></i>New Password
                            </label>
                            <input type="password" class="form-control" id="new_password" name="new_password"
                                   required minlength="<?php echo ADMIN_PASSWORD_MIN_LENGTH; ?>">
                            <div class="form-text">
                                Password must be at least <?php echo ADMIN_PASSWORD_MIN_LENGTH; ?> characters long.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="bi bi-check-circle me-1"></i>Confirm New Password
                            </label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="invalid-feedback" id="password-mismatch">
                                Passwords do not match.
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Security Note:</strong> You will remain logged in after changing your password.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="changePasswordBtn">
                            <i class="bi bi-check-lg me-1"></i>Change Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
