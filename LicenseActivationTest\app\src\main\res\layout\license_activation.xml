<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_background"
    tools:context=".activity.MainActivity">

    <!-- Scrollable Content Area -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="24dp"
                android:paddingTop="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="License Activation"
                    android:textSize="32sp"
                    android:textStyle="bold"
                    android:textColor="#FFFFFF"
                    android:layout_marginBottom="8dp"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Enter your license key and PIN to activate"
                    android:textSize="16sp"
                    android:textColor="#E3F2FD"
                    android:gravity="center"
                    android:fontFamily="sans-serif-light" />

            </LinearLayout>

            <!-- License Key Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:elevation="8dp"
                app:cardCornerRadius="16dp"
                app:cardBackgroundColor="#FFFFFF">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="License Key"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#424242"
                        android:layout_marginBottom="12dp"
                        android:fontFamily="sans-serif-medium" />

                    <EditText
                        android:id="@+id/licenseKeyField"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:hint="Enter your license key"
                        android:inputType="text"
                        android:background="@drawable/modern_edit_text_background"
                        android:padding="16dp"
                        android:textSize="16sp"
                        android:textColor="#212121"
                        android:textColorHint="#9E9E9E"
                        android:fontFamily="sans-serif" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- PIN Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:elevation="8dp"
                app:cardCornerRadius="16dp"
                app:cardBackgroundColor="#FFFFFF">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="PIN"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#424242"
                        android:layout_marginBottom="12dp"
                        android:fontFamily="sans-serif-medium" />

                    <EditText
                        android:id="@+id/pinField"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:hint="Enter your PIN"
                        android:inputType="numberPassword"
                        android:background="@drawable/modern_edit_text_background"
                        android:padding="16dp"
                        android:textSize="16sp"
                        android:textColor="#212121"
                        android:textColorHint="#9E9E9E"
                        android:fontFamily="sans-serif" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Activate Button -->
            <Button
                android:id="@+id/activateButton"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Activate License"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#FFFFFF"
                android:background="@drawable/modern_button_background"
                android:layout_marginBottom="20dp"
                android:elevation="8dp"
                android:fontFamily="sans-serif-medium"
                android:textAllCaps="false" />

            <!-- Status Text -->
            <TextView
                android:id="@+id/statusText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Please enter your license key and PIN to activate"
                android:textSize="15sp"
                android:textColor="#4CAF50"
                android:gravity="center"
                android:padding="16dp"
                android:background="@drawable/modern_status_background"
                android:layout_marginBottom="16dp"
                android:fontFamily="sans-serif"
                android:textStyle="bold" />

            <!-- Domain Login Section -->
            <LinearLayout
                android:id="@+id/domainLoginSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                android:layout_marginBottom="16dp">

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:elevation="8dp"
                    app:cardCornerRadius="16dp"
                    app:cardBackgroundColor="#FFFFFF">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="12dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🌐"
                                android:textSize="24sp"
                                android:layout_marginEnd="8dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Domain Access"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="#424242"
                                android:fontFamily="sans-serif-medium" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/domainInfoText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Your domain: Loading..."
                            android:textSize="15sp"
                            android:textColor="#666666"
                            android:layout_marginBottom="16dp"
                            android:fontFamily="sans-serif" />

                        <Button
                            android:id="@+id/domainLoginButton"
                            android:layout_width="match_parent"
                            android:layout_height="52dp"
                            android:text="🎛️ Open Dashboard"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#FFFFFF"
                            android:background="@drawable/modern_button_background"
                            android:elevation="4dp"
                            android:layout_marginBottom="12dp"
                            android:fontFamily="sans-serif-medium"
                            android:textAllCaps="false" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="💡 View your license details and domain information"
                            android:textSize="13sp"
                            android:textColor="#9E9E9E"
                            android:gravity="center"
                            android:fontFamily="sans-serif-light" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <!-- License Expiration Section -->
            <LinearLayout
                android:id="@+id/expirationSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                android:layout_marginBottom="16dp">

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:elevation="8dp"
                    app:cardCornerRadius="16dp"
                    app:cardBackgroundColor="#FFFFFF">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="16dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📅"
                                android:textSize="24sp"
                                android:layout_marginEnd="8dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="License Status"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="#424242"
                                android:fontFamily="sans-serif-medium" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/expirationText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="License expires: Loading..."
                            android:textSize="15sp"
                            android:textColor="#4CAF50"
                            android:layout_marginBottom="12dp"
                            android:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/countdownText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Time remaining: Calculating..."
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#4CAF50"
                            android:layout_marginBottom="16dp"
                            android:fontFamily="sans-serif-medium" />

                        <ProgressBar
                            android:id="@+id/expirationProgressBar"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="match_parent"
                            android:layout_height="12dp"
                            android:progress="100"
                            android:progressTint="#4CAF50"
                            android:progressBackgroundTint="#E8F5E8"
                            android:layout_marginBottom="12dp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="💡 You will receive warnings 2 days before expiration"
                            android:textSize="13sp"
                            android:textColor="#9E9E9E"
                            android:gravity="center"
                            android:fontFamily="sans-serif-light" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Footer Section (Always Visible at Bottom) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/footer_background"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="4dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="👨‍💻"
                android:textSize="16sp"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Md Sadrul Hasan Dider"
                android:textSize="14sp"
                android:textColor="#FFFFFF"
                android:textStyle="bold"
                android:fontFamily="sans-serif-medium" />

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="© 2025 License Activation System"
            android:textSize="12sp"
            android:textColor="#E3F2FD"
            android:gravity="center"
            android:fontFamily="sans-serif-light" />

    </LinearLayout>

</LinearLayout>