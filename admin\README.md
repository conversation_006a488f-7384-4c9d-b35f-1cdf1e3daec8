# Admin Panel - AppyStore MRecharge

## Overview
This is a modern, secure admin panel for managing licenses, devices, and authentication for the AppyStore MRecharge application.

## Features
- ✅ **Modern Login Design** - Beautiful, responsive login interface
- ✅ **Database Authentication** - Secure user authentication with MySQL
- ✅ **Plain Text Passwords** - Simple password management (as requested)
- ✅ **Session Management** - Secure session handling with timeout
- ✅ **Change Password** - Built-in password change functionality
- ✅ **Logout Option** - Secure logout with confirmation
- ✅ **Config File** - Centralized configuration management
- ✅ **Account Lockout** - Protection against brute force attacks
- ✅ **Responsive Design** - Works on desktop and mobile devices

## Installation

### 1. Database Setup
Run the SQL script to create the admin users table:
```sql
-- Import the setup_admin.sql file or run these commands:
SOURCE admin/setup_admin.sql;
```

### 2. Configuration
The `config.php` file contains all configuration settings. Update these as needed:
- Database connection details
- Security settings
- Application settings

### 3. Default Login Credentials
- **Username:** `admin`
- **Password:** `admin123`

⚠️ **Important:** Change the default password immediately after first login!

## Password Management

### Changing Admin Password via MySQL
To change the admin password manually via MySQL:

```sql
-- Change password for admin user
UPDATE admin_users SET password = 'your_new_password' WHERE username = 'admin';

-- Example:
UPDATE admin_users SET password = 'mySecurePassword123' WHERE username = 'admin';
```

### Creating New Admin Users
```sql
-- Create a new admin user
INSERT INTO admin_users (username, password, full_name, email, role) 
VALUES ('newadmin', 'password123', 'New Admin Name', '<EMAIL>', 'admin');

-- Create a moderator user
INSERT INTO admin_users (username, password, full_name, email, role) 
VALUES ('moderator', 'modpass123', 'Moderator Name', '<EMAIL>', 'moderator');
```

### User Management
```sql
-- Deactivate a user
UPDATE admin_users SET status = 'inactive' WHERE username = 'username';

-- Reactivate a user
UPDATE admin_users SET status = 'active' WHERE username = 'username';

-- Reset login attempts and unlock user
UPDATE admin_users SET login_attempts = 0, locked_until = NULL WHERE username = 'username';
```

## Security Features

### Account Lockout
- Maximum 5 failed login attempts
- 15-minute lockout period after max attempts
- Automatic unlock after lockout period

### Session Security
- 1-hour session timeout
- Secure session management
- Automatic logout on timeout

### Password Requirements
- Minimum 6 characters (configurable)
- Plain text storage (as requested)

## File Structure
```
admin/
├── config.php              # Main configuration file
├── login.php               # Modern login page
├── dashboard.php           # Main dashboard with logout/change password
├── licenses.php            # License management
├── devices.php             # Device management
├── settings.php            # System settings
├── api/
│   └── device_auth.php     # API for device authentication
├── setup_admin.sql         # Database setup script
└── README.md              # This file
```

## Configuration Options

### Database Settings (config.php)
```php
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'appystore_mrecharge');
```

### Security Settings
```php
define('ADMIN_PASSWORD_MIN_LENGTH', 6);
define('ADMIN_MAX_LOGIN_ATTEMPTS', 5);
define('ADMIN_LOCKOUT_TIME', 900); // 15 minutes
define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hour
```

### Application Settings
```php
define('ADMIN_PANEL_TITLE', 'AppyStore MRecharge Admin');
define('ADMIN_PANEL_VERSION', '2.0');
define('DEFAULT_TIMEZONE', 'Asia/Dhaka');
```

## Usage

### Accessing the Admin Panel
1. Navigate to `http://yourserver/admin/`
2. Login with your credentials
3. Use the dashboard to manage licenses and devices

### Changing Password
1. Click on your name in the top-right corner
2. Select "Change Password"
3. Enter current password and new password
4. Click "Change Password"

### Logging Out
1. Click on your name in the top-right corner
2. Select "Sign Out"
3. Confirm logout

## Troubleshooting

### Can't Login
1. Check if admin_users table exists
2. Verify default admin user exists
3. Check database connection in config.php
4. Reset login attempts if account is locked

### Password Reset
If you forget the admin password, reset it via MySQL:
```sql
UPDATE admin_users SET password = 'newpassword', login_attempts = 0, locked_until = NULL WHERE username = 'admin';
```

### Session Issues
1. Check session timeout settings
2. Verify PHP session configuration
3. Clear browser cookies/cache

## Support
For support or questions, contact the development team.

## Version History
- **v2.0** - Modern redesign with database authentication
- **v1.0** - Initial hardcoded authentication version
