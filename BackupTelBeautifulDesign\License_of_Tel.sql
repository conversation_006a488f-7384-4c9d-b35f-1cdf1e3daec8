-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: May 26, 2025 at 02:16 PM
-- Server version: 10.4.27-MariaDB
-- PHP Version: 8.2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `appystore_mrecharge`
--

-- --------------------------------------------------------

--
-- Table structure for table `auth_logs`
--

CREATE TABLE `auth_logs` (
  `id` int(11) NOT NULL,
  `license_key` varchar(255) NOT NULL,
  `pin` varchar(50) NOT NULL,
  `device_id` varchar(255) NOT NULL,
  `device_info` text DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `package` varchar(50) DEFAULT NULL,
  `version` varchar(20) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `approval_status` varchar(20) DEFAULT 'pending',
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `auth_logs`
--

INSERT INTO `auth_logs` (`id`, `license_key`, `pin`, `device_id`, `device_info`, `type`, `package`, `version`, `status`, `approval_status`, `approved_at`, `created_at`) VALUES
(159, 'TEST-VALID-LONG', '4444', '741616b100b4f521', 'samsung SM-M127G (Android 13)', 'server', 'premium', '1', 'success', 'approved', '2025-05-26 11:46:06', '2025-05-26 11:45:50'),
(161, 'TEST-VALID-LONG', '4444', '741616b100b4f521', 'samsung SM-M127G (Android 13)', 'server', 'premium', '1', 'success', 'approved', '2025-05-26 11:47:46', '2025-05-26 11:47:41'),
(165, 'TEST-VALID-LONG', '4444', '741616b100b4f521', 'samsung SM-M127G (Android 13)', 'server', 'premium', '1', 'success', 'approved', '2025-05-26 11:53:26', '2025-05-26 11:53:19'),
(169, 'TEST-VALID-LONG', '4444', '741616b100b4f521', 'samsung SM-M127G (Android 13)', 'server', 'premium', '1', 'success', 'approved', '2025-05-26 12:04:39', '2025-05-26 12:04:30');

-- --------------------------------------------------------

--
-- Table structure for table `blocked_devices`
--

CREATE TABLE `blocked_devices` (
  `id` int(11) NOT NULL,
  `device_id` varchar(255) NOT NULL,
  `reason` text DEFAULT NULL,
  `blocked_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `licenses`
--

CREATE TABLE `licenses` (
  `id` int(11) NOT NULL,
  `license_key` varchar(255) NOT NULL,
  `pin` varchar(50) NOT NULL,
  `package` varchar(50) NOT NULL,
  `domain` varchar(255) DEFAULT 'appystore.com',
  `status` tinyint(1) DEFAULT 1,
  `max_devices` int(11) DEFAULT 1,
  `expires_at` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `licenses`
--

INSERT INTO `licenses` (`id`, `license_key`, `pin`, `package`, `domain`, `status`, `max_devices`, `expires_at`, `created_at`, `updated_at`) VALUES
(22, 'LIC-YBFQ1UPZ', '8132', 'platinum', 'diderappstore.top', 1, 1, '2026-05-04', '2025-05-04 21:08:09', '2025-05-04 21:08:09'),
(23, 'TEST-EXPIRED', '1111', 'test', '192.168.0.106', 1, 1, '2025-05-25', '2025-05-26 10:20:39', '2025-05-26 11:37:32'),
(24, 'TEST-EXPIRING-SOON', '2222', 'test', '192.168.0.106', 1, 1, '2025-05-26', '2025-05-26 10:20:39', '2025-05-26 10:20:39'),
(25, 'TEST-WARNING-PERIOD', '3333', 'test', '192.168.0.106', 1, 1, '2025-05-27', '2025-05-26 10:20:39', '2025-05-26 11:25:32'),
(26, 'TEST-VALID-LONG', '4444', 'test', '192.168.0.106', 1, 1, '2025-06-25', '2025-05-26 10:20:39', '2025-05-26 10:20:39');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(255) NOT NULL,
  `setting_value` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
(1, 'app_version', '1.0', '2025-05-03 18:33:29', '2025-05-03 18:33:29'),
(2, 'min_app_version', '1.0', '2025-05-03 18:33:29', '2025-05-03 18:33:29'),
(3, 'allow_demo_licenses', '1', '2025-05-03 18:33:29', '2025-05-03 18:33:29'),
(4, 'max_devices_per_license', '5', '2025-05-03 18:33:29', '2025-05-03 18:33:29'),
(5, 'license_duration_days', '365', '2025-05-03 18:33:29', '2025-05-03 18:33:29'),
(6, 'use_https', '0', '2025-05-03 18:33:29', '2025-05-03 18:33:29');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `auth_logs`
--
ALTER TABLE `auth_logs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `blocked_devices`
--
ALTER TABLE `blocked_devices`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `device_id` (`device_id`);

--
-- Indexes for table `licenses`
--
ALTER TABLE `licenses`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `license_key` (`license_key`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `auth_logs`
--
ALTER TABLE `auth_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=170;

--
-- AUTO_INCREMENT for table `blocked_devices`
--
ALTER TABLE `blocked_devices`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `licenses`
--
ALTER TABLE `licenses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
