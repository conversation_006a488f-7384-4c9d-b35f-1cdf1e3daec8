<?php
/**
 * Device Authentication API
 *
 * This API handles device authentication requests from the Android app.
 * It validates license keys and manages device registrations.
 */

// Set headers for JSON response
header('Content-Type: application/json');

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include configuration
require_once '../config.php';

// Get database connection
$conn = getDbConnection();

// Check if this is a device block status check
if (isset($_POST['action']) && $_POST['action'] === 'check_block_status') {
    // Handle device block status check request
    $device_id = isset($_POST['device_id']) ? trim($_POST['device_id']) : '';

    if (empty($device_id)) {
        sendErrorResponse("Missing device_id parameter");
        exit;
    }

    // Check if device is blocked
    $blocked_info = checkIfDeviceBlocked($conn, $device_id);
    if ($blocked_info !== false) {
        // Device is blocked
        $response = [
            [
                'status' => 4, // Status 4 for blocked device
                'version' => 1,
                'message' => 'Your device has been blocked by the administrator',
                'block_reason' => $blocked_info['reason'],
                'blocked_at' => $blocked_info['created_at'],
                'is_blocked' => true
            ]
        ];
    } else {
        // Device is not blocked
        $response = [
            [
                'status' => 1,
                'version' => 1,
                'message' => 'Device is not blocked',
                'is_blocked' => false
            ]
        ];
    }

    echo json_encode($response);
    exit;
}

// Check if this is an approval status check (new format)
if (isset($_POST['action']) && $_POST['action'] === 'check_approval_status') {
    // Handle approval status check request
    $log_id = isset($_POST['log_id']) ? (int)$_POST['log_id'] : 0;

    if ($log_id <= 0) {
        sendErrorResponse("Invalid log ID");
        exit;
    }

    // Check the approval status of the log entry
    $stmt = $conn->prepare("SELECT approval_status, status, license_key, pin, device_id FROM auth_logs WHERE id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $log_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $approval_status = $row['approval_status'];
            $license_key = $row['license_key'];
            $pin = $row['pin'];
            $device_id = $row['device_id'];

            // Check if device is blocked first
            $blocked_info = checkIfDeviceBlocked($conn, $device_id);
            if ($blocked_info !== false) {
                // Device is blocked, return blocked status
                $response = [
                    [
                        'status' => 4, // Status 4 for blocked device
                        'version' => 1,
                        'message' => 'Your device has been blocked by the administrator',
                        'block_reason' => $blocked_info['reason'],
                        'blocked_at' => $blocked_info['created_at'],
                        'log_id' => $log_id
                    ]
                ];
            } elseif ($approval_status === 'approved') {
                // Get license details for approved request
                $license_stmt = $conn->prepare("SELECT domain, expires_at, package FROM licenses WHERE license_key = ? AND pin = ?");
                if ($license_stmt) {
                    $license_stmt->bind_param("ss", $license_key, $pin);
                    $license_stmt->execute();
                    $license_result = $license_stmt->get_result();

                    if ($license_result->num_rows > 0) {
                        $license_row = $license_result->fetch_assoc();
                        $domain = $license_row['domain'];
                        $expires_at = $license_row['expires_at'];
                        $package = $license_row['package'];

                        // Return success response with license details
                        $response = [
                            [
                                'status' => 1,
                                'version' => 1,
                                'message' => 'License validated successfully',
                                'domain' => $domain,
                                'sec' => 0, // 0 for http, 1 for https
                                'expires' => $expires_at,
                                'package' => $package,
                                'device_id' => $device_id,
                                'server_license_key' => $license_key,
                                'log_id' => $log_id
                            ]
                        ];
                    } else {
                        // License not found (shouldn't happen if approved)
                        $response = [
                            [
                                'status' => 0,
                                'version' => 1,
                                'message' => 'License not found',
                                'log_id' => $log_id
                            ]
                        ];
                    }
                    $license_stmt->close();
                } else {
                    sendErrorResponse("Database error: " . $conn->error);
                    exit;
                }
            } elseif ($approval_status === 'rejected') {
                // Return rejection response
                $response = [
                    [
                        'status' => 3,
                        'version' => 1,
                        'message' => 'Your authentication request has been rejected by the administrator',
                        'log_id' => $log_id
                    ]
                ];
            } else {
                // Still pending
                $response = [
                    [
                        'status' => 2,
                        'version' => 1,
                        'message' => 'Your device is still pending approval by the administrator',
                        'log_id' => $log_id
                    ]
                ];
            }
        } else {
            $response = [
                [
                    'status' => 0,
                    'version' => 1,
                    'message' => 'Authentication request not found',
                    'log_id' => $log_id
                ]
            ];
        }

        $stmt->close();
        echo json_encode($response);
        exit;
    } else {
        sendErrorResponse("Database error: " . $conn->error);
        exit;
    }
}

// Check if this is an approval status check (legacy format)
if (isset($_POST['check_approval']) && $_POST['check_approval'] === '1') {
    // Handle approval status check
    if (!isset($_POST['log_id']) || empty($_POST['log_id'])) {
        sendErrorResponse("Missing log_id parameter");
        exit;
    }

    $log_id = (int)$_POST['log_id'];
    $device_id = isset($_POST['device_id']) ? $_POST['device_id'] : '';
    $license_key = isset($_POST['license_key']) ? $_POST['license_key'] : '';

    // Get the approval status
    $stmt = $conn->prepare("SELECT approval_status FROM auth_logs WHERE id = ? AND device_id = ? AND license_key = ?");
    if ($stmt) {
        $stmt->bind_param("iss", $log_id, $device_id, $license_key);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $approval_status = $row['approval_status'];

            if ($approval_status === 'approved') {
                // Approved
                $response = [
                    [
                        'status' => 1,
                        'version' => 1,
                        'message' => 'Your device has been approved'
                    ]
                ];
            } else if ($approval_status === 'rejected') {
                // Rejected
                $response = [
                    [
                        'status' => 0,
                        'version' => 1,
                        'message' => 'Your device has been rejected'
                    ]
                ];
            } else {
                // Still pending
                $response = [
                    [
                        'status' => 2,
                        'version' => 1,
                        'message' => 'Your device is still pending approval'
                    ]
                ];
            }
        } else {
            // Log entry not found
            $response = [
                [
                    'status' => 0,
                    'version' => 1,
                    'message' => 'Invalid log ID or device ID'
                ]
            ];
        }

        $stmt->close();
        echo json_encode($response);
        exit;
    } else {
        sendErrorResponse("Database error: " . $conn->error);
        exit;
    }
}

// Normal authentication flow
// Required parameters
$required_params = ['license_key', 'pin', 'device_id', 'device_info', 'type', 'pkg', 'version'];
$missing_params = [];

// Check for required parameters
foreach ($required_params as $param) {
    if (!isset($_POST[$param]) || empty($_POST[$param])) {
        $missing_params[] = $param;
    }
}

// If any required parameters are missing, return error
if (!empty($missing_params)) {
    sendErrorResponse("Missing required parameters: " . implode(', ', $missing_params));
    exit;
}

// Get parameters from request
$license_key = $_POST['license_key'];
$pin = $_POST['pin'];
$device_id = $_POST['device_id'];
$device_info = $_POST['device_info'];
$type = $_POST['type'];
$pkg = $_POST['pkg'];
$version = $_POST['version'];
$timestamp = isset($_POST['timestamp']) ? $_POST['timestamp'] : time() * 1000;

// For demo purposes, accept any license key that starts with "DEMO-"
$is_demo = (strpos($license_key, 'DEMO-') === 0);

// For testing, also accept "test-license" with pin "1234"
$is_test = ($license_key === 'test-license' && $pin === '1234');

// Check if this is a valid license key in the database
$is_valid_license = false;
$domain = 'appystore.com'; // Default domain
$package = 'platinum'; // Default package
$expires_at = date('Y-m-d', strtotime('+30 days')); // Default expiration

// Check if the license exists in the database
$stmt = $conn->prepare("SELECT * FROM licenses WHERE license_key = ? AND pin = ?");
if ($stmt) {
    $stmt->bind_param("ss", $license_key, $pin);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $is_valid_license = true;

        // Get license details from database
        $domain = isset($row['domain']) ? $row['domain'] : 'appystore.com';
        $package = $row['package'];
        $expires_at = $row['expires_at'];

        // Log the license details for debugging
        error_log("License found in database: " . print_r($row, true));
    }
    $stmt->close();
}

// Check if this device is blocked first
$blocked_info = checkIfDeviceBlocked($conn, $device_id);
if ($blocked_info !== false) {
    // Device is blocked, return blocked status
    $response = [
        [
            'status' => 4, // Status 4 for blocked device
            'version' => 1,
            'message' => 'Your device has been blocked by the administrator',
            'block_reason' => $blocked_info['reason'],
            'blocked_at' => $blocked_info['created_at']
        ]
    ];

    // Log this blocked authentication attempt
    logAuthAttempt($conn, $license_key, $pin, $device_id, $device_info, $type, $pkg, $version, 'blocked');

    echo json_encode($response);
    exit;
}

// Check if this is a valid license key (demo, test, or valid from database)
if ($is_demo || $is_test || $is_valid_license) {
    // For demo/test licenses, we'll use the default values
    // For valid licenses, we'll use the values from the database

    // Log the domain being used
    error_log("Using domain: " . $domain);

    // Check if this device has been approved before
    $is_approved = false;
    $log_id = 0;

    // For demo or test licenses, auto-approve
    if ($is_demo || $is_test) {
        $is_approved = true;
    } else {
        // Check if this device+license combination has been approved before
        $stmt = $conn->prepare("SELECT id, approval_status FROM auth_logs
                               WHERE license_key = ? AND device_id = ?
                               AND approval_status IN ('approved', 'pending')
                               ORDER BY created_at DESC LIMIT 1");
        if ($stmt) {
            $stmt->bind_param("ss", $license_key, $device_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $row = $result->fetch_assoc();
                $log_id = $row['id'];
                $is_approved = ($row['approval_status'] === 'approved');
            }
            $stmt->close();
        }
    }

    // Log this authentication attempt
    $log_id = logAuthAttempt($conn, $license_key, $pin, $device_id, $device_info, $type, $pkg, $version, 'success');

    if ($is_approved) {
        // Device is approved, grant access
        $response = [
            [
                'status' => 1,
                'version' => 1,
                'message' => 'License validated successfully',
                'domain' => $domain,
                'sec' => 0, // 0 for http, 1 for https
                'expires' => $expires_at,
                'package' => $package,
                'device_id' => $device_id,
                'server_license_key' => $license_key // Include the server license key in the response
            ]
        ];
    } else {
        // Device needs approval
        $response = [
            [
                'status' => 2, // New status code for pending approval
                'version' => 1,
                'message' => 'Your device is pending approval by the administrator. Please try again later.',
                'log_id' => $log_id
            ]
        ];
    }

    echo json_encode($response);
    exit;
} else {
    // License key and PIN combination not found in the database

    // Check if the license key exists but with a different PIN
    $stmt = $conn->prepare("SELECT id FROM licenses WHERE license_key = ?");
    if ($stmt) {
        $stmt->bind_param("s", $license_key);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            // License key exists but PIN is incorrect
            $error_message = 'Invalid PIN for this license key';
        } else {
            // License key doesn't exist
            $error_message = 'Invalid license key';
        }
        $stmt->close();
    } else {
        $error_message = 'Database error';
    }

    // Log this failed authentication attempt
    logAuthAttempt($conn, $license_key, $pin, $device_id, $device_info, $type, $pkg, $version, 'failed');

    $response = [
        [
            'status' => 0,
            'version' => 1,
            'message' => $error_message
        ]
    ];

    echo json_encode($response);
    exit;
}

/**
 * Check if a device is blocked
 */
function checkIfDeviceBlocked($conn, $device_id) {
    // Create blocked_devices table if it doesn't exist
    $create_table_sql = "CREATE TABLE IF NOT EXISTS blocked_devices (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        device_id VARCHAR(255) NOT NULL UNIQUE,
        reason TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    $conn->query($create_table_sql);

    // Check if created_at column exists, if not add it
    $check_column = $conn->query("SHOW COLUMNS FROM blocked_devices LIKE 'created_at'");
    if ($check_column && $check_column->num_rows === 0) {
        $conn->query("ALTER TABLE blocked_devices ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
    }

    // Check if device is blocked
    try {
        $stmt = $conn->prepare("SELECT reason, created_at FROM blocked_devices WHERE device_id = ?");
        if ($stmt) {
            $stmt->bind_param("s", $device_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $row = $result->fetch_assoc();
                $stmt->close();
                return [
                    'reason' => $row['reason'] ?: 'No reason provided',
                    'created_at' => $row['created_at'] ?: date('Y-m-d H:i:s')
                ];
            }
            $stmt->close();
        }
    } catch (Exception $e) {
        // If there's an error checking blocked devices, log it but don't block authentication
        error_log("Error checking blocked devices: " . $e->getMessage());
    }

    return false; // Device is not blocked
}

/**
 * Log authentication attempt to database
 */
function logAuthAttempt($conn, $license_key, $pin, $device_id, $device_info, $type, $pkg, $version, $status) {
    // Create auth_logs table if it doesn't exist
    $create_table_sql = "CREATE TABLE IF NOT EXISTS auth_logs (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        license_key VARCHAR(255) NOT NULL,
        pin VARCHAR(50) NOT NULL,
        device_id VARCHAR(255) NOT NULL,
        device_info TEXT,
        type VARCHAR(50),
        package VARCHAR(50),
        version VARCHAR(20),
        status VARCHAR(20),
        approval_status VARCHAR(20) DEFAULT 'pending',
        approved_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    $conn->query($create_table_sql);

    // Check if approval_status column exists, if not add it
    $check_column = $conn->query("SHOW COLUMNS FROM auth_logs LIKE 'approval_status'");
    if ($check_column->num_rows === 0) {
        $conn->query("ALTER TABLE auth_logs ADD COLUMN approval_status VARCHAR(20) DEFAULT 'pending' AFTER status");
    }

    // Check if approved_at column exists, if not add it
    $check_column = $conn->query("SHOW COLUMNS FROM auth_logs LIKE 'approved_at'");
    if ($check_column->num_rows === 0) {
        $conn->query("ALTER TABLE auth_logs ADD COLUMN approved_at TIMESTAMP NULL AFTER approval_status");
    }

    // Set default approval status
    $approval_status = 'pending';

    // Auto-approve for demo or test licenses
    if (strpos($license_key, 'DEMO-') === 0 || $license_key === 'test-license') {
        $approval_status = 'approved';
    }

    // Insert log entry
    $stmt = $conn->prepare("INSERT INTO auth_logs (license_key, pin, device_id, device_info, type, package, version, status, approval_status, approved_at)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, " . ($approval_status === 'approved' ? 'NOW()' : 'NULL') . ")");

    $log_id = 0;
    if ($stmt) {
        $stmt->bind_param("sssssssss", $license_key, $pin, $device_id, $device_info, $type, $pkg, $version, $status, $approval_status);
        $stmt->execute();
        $log_id = $stmt->insert_id;
        $stmt->close();
    }

    return $log_id;
}

/**
 * Send error response
 */
function sendErrorResponse($message) {
    $response = [
        [
            'status' => 0,
            'message' => $message
        ]
    ];

    echo json_encode($response);
}
?>
