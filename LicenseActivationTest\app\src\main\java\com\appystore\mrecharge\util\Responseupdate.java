package com.appystore.mrecharge.util;

import com.appystore.mrecharge.WResponse;
import retrofit2.Call;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

public interface Responseupdate {
    @FormUrlEncoded
    @POST("/modemcon/updateres")
    Call<WResponse> getresupdate(@Field("pass") String str, @Field("sender") String str2, @Field("body") String str3, @Field("ref") String str4);
}
