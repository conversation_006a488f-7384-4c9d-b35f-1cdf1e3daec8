<?php
/**
 * Test Block Functionality
 * 
 * This script tests the device blocking functionality and API responses.
 */

// Test configuration
$api_url = 'http://*************/AppyStoreMRecharge/admin/api/device_auth.php';
$test_device_id = 'TEST-DEVICE-' . time();

echo "<h1>Device Block Functionality Test</h1>\n";
echo "<p>Testing API at: <strong>$api_url</strong></p>\n";
echo "<p>Test Device ID: <strong>$test_device_id</strong></p>\n";

// Function to make POST request
function makePostRequest($url, $data) {
    $postData = http_build_query($data);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/x-www-form-urlencoded',
            'content' => $postData
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    return json_decode($response, true);
}

// Test 1: Check block status for non-blocked device
echo "<h2>Test 1: Check Block Status (Not Blocked)</h2>\n";
$blockCheckData = [
    'action' => 'check_block_status',
    'device_id' => $test_device_id
];

echo "<p>Checking block status for device...</p>\n";
$blockResponse = makePostRequest($api_url, $blockCheckData);

if ($blockResponse) {
    echo "<pre>" . json_encode($blockResponse, JSON_PRETTY_PRINT) . "</pre>\n";
    
    if (isset($blockResponse[0]['status']) && $blockResponse[0]['status'] == 1) {
        echo "<p style='color: green;'>✅ Test 1 PASSED: Device is not blocked</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Test 1 FAILED: Unexpected response</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Test 1 FAILED: No response from API</p>\n";
}

// Test 2: Block the device (simulate admin action)
echo "<h2>Test 2: Block Device</h2>\n";

// Database connection to block the device
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'appystore_mrecharge';

$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $conn->connect_error . "</p>\n";
} else {
    // Create blocked_devices table if it doesn't exist
    $create_table_sql = "CREATE TABLE IF NOT EXISTS blocked_devices (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        device_id VARCHAR(255) NOT NULL UNIQUE,
        reason TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $conn->query($create_table_sql);
    
    // Block the test device
    $block_reason = 'Test blocking for functionality verification';
    $stmt = $conn->prepare("INSERT INTO blocked_devices (device_id, reason) VALUES (?, ?) ON DUPLICATE KEY UPDATE reason = VALUES(reason)");
    $stmt->bind_param("ss", $test_device_id, $block_reason);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ Device blocked successfully in database</p>\n";
        echo "<p>Reason: " . htmlspecialchars($block_reason) . "</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Failed to block device: " . $conn->error . "</p>\n";
    }
    
    $stmt->close();
}

// Test 3: Check block status for blocked device
echo "<h2>Test 3: Check Block Status (Blocked)</h2>\n";

echo "<p>Checking block status for blocked device...</p>\n";
$blockResponse2 = makePostRequest($api_url, $blockCheckData);

if ($blockResponse2) {
    echo "<pre>" . json_encode($blockResponse2, JSON_PRETTY_PRINT) . "</pre>\n";
    
    if (isset($blockResponse2[0]['status']) && $blockResponse2[0]['status'] == 4) {
        echo "<p style='color: green;'>✅ Test 3 PASSED: Device is correctly identified as blocked</p>\n";
        
        if (isset($blockResponse2[0]['block_reason'])) {
            echo "<p>Block reason: " . htmlspecialchars($blockResponse2[0]['block_reason']) . "</p>\n";
        }
        
        if (isset($blockResponse2[0]['blocked_at'])) {
            echo "<p>Blocked at: " . htmlspecialchars($blockResponse2[0]['blocked_at']) . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ Test 3 FAILED: Device should be blocked but isn't</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Test 3 FAILED: No response from API</p>\n";
}

// Test 4: Test authentication with blocked device
echo "<h2>Test 4: Authentication with Blocked Device</h2>\n";

$authData = [
    'license_key' => 'DEMO-123456',
    'pin' => '1234',
    'device_id' => $test_device_id,
    'device_info' => 'Test Device (Block Test)',
    'type' => 'server',
    'pkg' => 'premium',
    'version' => '1',
    'timestamp' => time() * 1000
];

echo "<p>Attempting authentication with blocked device...</p>\n";
$authResponse = makePostRequest($api_url, $authData);

if ($authResponse) {
    echo "<pre>" . json_encode($authResponse, JSON_PRETTY_PRINT) . "</pre>\n";
    
    if (isset($authResponse[0]['status']) && $authResponse[0]['status'] == 4) {
        echo "<p style='color: green;'>✅ Test 4 PASSED: Authentication correctly blocked</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Test 4 FAILED: Authentication should be blocked</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Test 4 FAILED: No response from API</p>\n";
}

// Test 5: Unblock device and test again
echo "<h2>Test 5: Unblock Device and Test</h2>\n";

if (!$conn->connect_error) {
    // Unblock the device
    $stmt = $conn->prepare("DELETE FROM blocked_devices WHERE device_id = ?");
    $stmt->bind_param("s", $test_device_id);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ Device unblocked successfully</p>\n";
        
        // Test block status again
        echo "<p>Checking block status after unblocking...</p>\n";
        $blockResponse3 = makePostRequest($api_url, $blockCheckData);
        
        if ($blockResponse3) {
            echo "<pre>" . json_encode($blockResponse3, JSON_PRETTY_PRINT) . "</pre>\n";
            
            if (isset($blockResponse3[0]['status']) && $blockResponse3[0]['status'] == 1) {
                echo "<p style='color: green;'>✅ Test 5 PASSED: Device is correctly unblocked</p>\n";
            } else {
                echo "<p style='color: red;'>❌ Test 5 FAILED: Device should be unblocked</p>\n";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to unblock device: " . $conn->error . "</p>\n";
    }
    
    $stmt->close();
    $conn->close();
}

echo "<h2>Test Summary</h2>\n";
echo "<p>All tests completed. Check the results above to verify the block functionality is working correctly.</p>\n";
echo "<p><strong>Note:</strong> The test device ID used was: <code>$test_device_id</code></p>\n";
?>
