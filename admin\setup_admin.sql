-- Admin Panel Setup SQL Script
-- Run this script to set up the admin users table and create default admin user

-- Create admin_users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin', 'moderator') DEFAULT 'admin',
    status ENUM('active', 'inactive') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    login_attempts INT(11) DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin user (password is stored as plain text)
-- Default credentials: admin / admin123
INSERT INTO admin_users (username, password, full_name, email, role) 
VALUES ('admin', 'admin123', 'System Administrator', '<EMAIL>', 'admin')
ON DUPLICATE KEY UPDATE 
    password = 'admin123',
    full_name = 'System Administrator',
    email = '<EMAIL>',
    status = 'active';

-- You can change the admin password by running:
-- UPDATE admin_users SET password = 'your_new_password' WHERE username = 'admin';

-- To create additional admin users, use:
-- INSERT INTO admin_users (username, password, full_name, email, role) 
-- VALUES ('newadmin', 'newpassword', 'New Admin Name', '<EMAIL>', 'admin');

-- To create a moderator user, use:
-- INSERT INTO admin_users (username, password, full_name, email, role) 
-- VALUES ('moderator', 'modpassword', 'Moderator Name', '<EMAIL>', 'moderator');

-- To deactivate a user:
-- UPDATE admin_users SET status = 'inactive' WHERE username = 'username';

-- To reactivate a user:
-- UPDATE admin_users SET status = 'active' WHERE username = 'username';

-- To reset login attempts and unlock a user:
-- UPDATE admin_users SET login_attempts = 0, locked_until = NULL WHERE username = 'username';
