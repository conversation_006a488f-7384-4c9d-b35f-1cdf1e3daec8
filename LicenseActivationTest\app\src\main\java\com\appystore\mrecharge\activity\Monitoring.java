package com.appystore.mrecharge.activity;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.preference.PreferenceManager;
import androidx.appcompat.app.AppCompatActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.appystore.mrecharge.DbHelper;
import com.appystore.mrecharge.Items;
import com.appystore.mrecharge.R;
import com.appystore.mrecharge.RecyclerviewItemAdapter;
import java.util.ArrayList;
import java.util.List;

/* loaded from: classes.dex */
public class Monitoring extends AppCompatActivity {
    private SQLiteDatabase dataBase;
    IntentFilter filter;
    private List<Items> itemsList;
    private DbHelper mydb;
    private RecyclerView recyclerView;
    private RecyclerviewItemAdapter recyclerviewItemAdapter;
    String servname;
    Cursor cursor = null;
    BroadcastReceiver receiver = new BroadcastReceiver() { // from class: com.flexisoftwarebd.serverpro.activity.Monitoring.1
        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction().equals("BackgroundService")) {
                Monitoring.this.prepareItems(intent.getExtras().getString(DbHelper.CONTACTS_COLUMN_ID), intent.getExtras().getString("act"));
            }
        }
    };

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(R.layout.listmain);

        try {
            // Initialize database helper
            this.mydb = new DbHelper(getApplicationContext());

            // Set up broadcast receiver filter
            this.filter = new IntentFilter();
            this.filter.addAction("BackgroundService");

            // Initialize items list
            this.itemsList = new ArrayList<>();

            // Set up RecyclerView
            this.recyclerView = (RecyclerView) findViewById(R.id.recycleView);
            if (this.recyclerView != null) {
                // Create and set adapter
                this.recyclerviewItemAdapter = new RecyclerviewItemAdapter(this.itemsList, getApplicationContext());

                // Configure RecyclerView
                this.recyclerView.setHasFixedSize(true);
                this.recyclerView.setLayoutManager(new LinearLayoutManager(getApplicationContext()));
                this.recyclerView.setItemAnimator(new DefaultItemAnimator());
                this.recyclerView.setAdapter(this.recyclerviewItemAdapter);

                // Load data
                displayData();
            } else {
                // Log error if RecyclerView not found
                android.util.Log.e("Monitoring", "RecyclerView not found in layout");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onResume() {
        LocalBroadcastManager.getInstance(this).registerReceiver(this.receiver, new IntentFilter("BackgroundService"));
        super.onResume();
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onDestroy() {
        LocalBroadcastManager.getInstance(this).unregisterReceiver(this.receiver);
        super.onDestroy();
    }

    public void SavePreferences(String str, String str2) {
        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit();
        edit.putString(str, str2);
        edit.commit();
    }

    private void displayData() {
        try {
            this.dataBase = this.mydb.getWritableDatabase();
            Cursor rawQuery = this.dataBase.rawQuery("select * from income_log ORDER BY ID DESC LIMIT 100", null);

            if (rawQuery != null && rawQuery.moveToFirst()) {
                do {
                    // Get column indices safely
                    int orderIdIndex = rawQuery.getColumnIndex("orderid");
                    int numberIndex = rawQuery.getColumnIndex(DbHelper.CONTACTS_COLUMN_NUMBER);
                    int amountIndex = rawQuery.getColumnIndex(DbHelper.CONTACTS_COLUMN_AMOUNT);
                    int apiResponseIndex = rawQuery.getColumnIndex("apiresponse");
                    int ussdIndex = rawQuery.getColumnIndex("ussd");
                    int statusIndex = rawQuery.getColumnIndex("status");

                    // Get values with null checks
                    String orderId = orderIdIndex >= 0 ? rawQuery.getString(orderIdIndex) : "N/A";
                    String number = numberIndex >= 0 ? rawQuery.getString(numberIndex) : "N/A";
                    String amount = amountIndex >= 0 ? rawQuery.getString(amountIndex) : "N/A";
                    String apiResponse = apiResponseIndex >= 0 ? rawQuery.getString(apiResponseIndex) : "N/A";
                    String ussd = ussdIndex >= 0 ? rawQuery.getString(ussdIndex) : "N/A";
                    int status = statusIndex >= 0 ? rawQuery.getInt(statusIndex) : 0;

                    // Build the display string
                    String displayText = "Order Id: " + orderId +
                                        "\nNumber: " + number +
                                        "\nAmount: " + amount +
                                        "\nResponse: " + apiResponse +
                                        "\nUssd: " + ussd + "#";

                    // Add to the list
                    this.itemsList.add(new Items(displayText, status));

                } while (rawQuery.moveToNext());

                // Notify adapter of changes
                if (this.recyclerviewItemAdapter != null) {
                    this.recyclerviewItemAdapter.notifyDataSetChanged();
                }
            }

            if (rawQuery != null) {
                rawQuery.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void prepareItems(String str, String str2) {
        try {
            if (str2.equals("in")) {
                Cursor data = this.mydb.getData(str);
                if (data != null && data.getCount() > 0) {
                    data.moveToFirst();

                    // Get column indices safely
                    int orderIdIndex = data.getColumnIndex("orderid");
                    int numberIndex = data.getColumnIndex(DbHelper.CONTACTS_COLUMN_NUMBER);
                    int amountIndex = data.getColumnIndex(DbHelper.CONTACTS_COLUMN_AMOUNT);
                    int apiResponseIndex = data.getColumnIndex("apiresponse");
                    int ussdIndex = data.getColumnIndex("ussd");
                    int statusIndex = data.getColumnIndex("status");

                    // Get values with null checks
                    String orderId = orderIdIndex >= 0 ? data.getString(orderIdIndex) : "N/A";
                    String number = numberIndex >= 0 ? data.getString(numberIndex) : "N/A";
                    String amount = amountIndex >= 0 ? data.getString(amountIndex) : "N/A";
                    String apiResponse = apiResponseIndex >= 0 ? data.getString(apiResponseIndex) : "N/A";
                    String ussd = ussdIndex >= 0 ? data.getString(ussdIndex) : "N/A";
                    int status = statusIndex >= 0 ? data.getInt(statusIndex) : 0;

                    // Build the display string
                    String displayText = "Order Id: " + orderId +
                                        "\nNumber: " + number +
                                        "\nAmount: " + amount +
                                        "\nResponse: " + apiResponse +
                                        "\nUssd: " + ussd + "#";

                    // Add to the list
                    this.itemsList.add(0, new Items(displayText, status));

                    // Close the cursor
                    data.close();
                }

                // Notify adapter of changes
                if (this.recyclerviewItemAdapter != null) {
                    this.recyclerviewItemAdapter.notifyDataSetChanged();
                }
                return;
            }

            // Clear and reload data
            clear();
            displayData();

            // Notify adapter of changes
            if (this.recyclerviewItemAdapter != null) {
                this.recyclerviewItemAdapter.notifyDataSetChanged();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void clear() {
        int size = this.itemsList.size();
        if (size > 0) {
            for (int i = 0; i < size; i++) {
                this.itemsList.remove(0);
            }
            this.recyclerviewItemAdapter.notifyItemRangeRemoved(0, size);
        }
    }

    public static String getPref(String str, String str2, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, str2);
    }
}