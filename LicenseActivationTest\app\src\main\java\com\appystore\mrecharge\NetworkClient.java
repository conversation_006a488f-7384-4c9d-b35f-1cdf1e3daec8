package com.appystore.mrecharge;

import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class NetworkClient {
    public static Retrofit retrofit;

    public static Retrofit getRetrofitClient(String str) {
        if (retrofit == null) {
            retrofit = new Retrofit.Builder().baseUrl(str).addConverterFactory(GsonConverterFactory.create()).build();
        }
        return retrofit;
    }
}
