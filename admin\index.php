<?php
/**
 * Admin Panel - Index Page
 *
 * This file redirects to the login page with a beautiful loading animation.
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include configuration
require_once 'config.php';

// Check if user is already logged in
if (checkAdminSession()) {
    header('Location: dashboard.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - <?php echo ADMIN_PANEL_TITLE; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 25%, #ec4899 50%, #f59e0b 75%, #10b981 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .loading-container {
            text-align: center;
            color: white;
            z-index: 10;
        }

        .logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .logo::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .logo i {
            font-size: 4rem;
            color: white;
            z-index: 1;
            position: relative;
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.1rem;
            font-weight: 600;
            opacity: 0.8;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }

        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s infinite ease-in-out;
        }

        .particle:nth-child(1) {
            width: 20px;
            height: 20px;
            left: 10%;
            animation-delay: 0s;
        }

        .particle:nth-child(2) {
            width: 15px;
            height: 15px;
            left: 20%;
            animation-delay: 1s;
        }

        .particle:nth-child(3) {
            width: 25px;
            height: 25px;
            left: 30%;
            animation-delay: 2s;
        }

        .particle:nth-child(4) {
            width: 18px;
            height: 18px;
            left: 40%;
            animation-delay: 3s;
        }

        .particle:nth-child(5) {
            width: 22px;
            height: 22px;
            left: 50%;
            animation-delay: 4s;
        }

        .particle:nth-child(6) {
            width: 16px;
            height: 16px;
            left: 60%;
            animation-delay: 5s;
        }

        .particle:nth-child(7) {
            width: 24px;
            height: 24px;
            left: 70%;
            animation-delay: 0.5s;
        }

        .particle:nth-child(8) {
            width: 19px;
            height: 19px;
            left: 80%;
            animation-delay: 1.5s;
        }

        .particle:nth-child(9) {
            width: 21px;
            height: 21px;
            left: 90%;
            animation-delay: 2.5s;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .version-info {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <div class="loading-container">
        <div class="logo">
            <i class="bi bi-shield-lock"></i>
        </div>
        <h1><?php echo ADMIN_PANEL_TITLE; ?></h1>
        <p class="subtitle">Secure Administration Portal</p>
        <div class="loading-spinner"></div>
        <p class="loading-text">Initializing secure connection...</p>
    </div>

    <div class="version-info">
        <i class="bi bi-info-circle me-1"></i>
        Version <?php echo ADMIN_PANEL_VERSION; ?> | Powered by AppyStore
    </div>

    <script>
        // Redirect to login page after animation
        setTimeout(function() {
            document.querySelector('.loading-text').textContent = 'Redirecting to login...';
            setTimeout(function() {
                window.location.href = 'login.php';
            }, 1000);
        }, 2000);
    </script>
</body>
</html>
