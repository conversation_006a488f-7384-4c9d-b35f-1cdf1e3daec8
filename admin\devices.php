<?php
/**
 * Admin Panel - Device Management
 *
 * This file handles device management functionality.
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include configuration
require_once 'config.php';

// Check if user is logged in
if (!checkAdminSession()) {
    header('Location: login.php');
    exit;
}

// Get database connection
$conn = getDbConnection();
$db_error = null;

// Set SQL mode to be less strict (remove ONLY_FULL_GROUP_BY)
if (!isset($db_error)) {
    // Get current SQL mode
    $result = $conn->query("SELECT @@sql_mode");
    if ($result && $row = $result->fetch_array()) {
        $sql_mode = $row[0];

        // Remove ONLY_FULL_GROUP_BY if present
        $modes = explode(',', $sql_mode);
        $modes = array_filter($modes, function($mode) {
            return trim($mode) !== 'ONLY_FULL_GROUP_BY';
        });

        // Set the new SQL mode
        $new_sql_mode = implode(',', $modes);
        $conn->query("SET SESSION sql_mode = '$new_sql_mode'");
    }
}

// Initialize variables
$devices = [];
$message = '';
$message_type = '';

// Create auth_logs table if it doesn't exist
if (!isset($db_error)) {
    $create_table_sql = "CREATE TABLE IF NOT EXISTS auth_logs (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        license_key VARCHAR(255) NOT NULL,
        pin VARCHAR(50) NOT NULL,
        device_id VARCHAR(255) NOT NULL,
        device_info TEXT,
        type VARCHAR(50),
        package VARCHAR(50),
        version VARCHAR(20),
        status VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    $conn->query($create_table_sql);
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Block device
    if (isset($_POST['action']) && $_POST['action'] === 'block_device') {
        $device_id = isset($_POST['device_id']) ? trim($_POST['device_id']) : '';

        if (!empty($device_id)) {
            // Create blocked_devices table if it doesn't exist
            $create_table_sql = "CREATE TABLE IF NOT EXISTS blocked_devices (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                device_id VARCHAR(255) NOT NULL UNIQUE,
                reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";

            $conn->query($create_table_sql);

            // Check if device is already blocked
            $stmt = $conn->prepare("SELECT id FROM blocked_devices WHERE device_id = ?");
            $stmt->bind_param("s", $device_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $message = 'Device is already blocked';
                $message_type = 'warning';
            } else {
                // Block device
                $reason = isset($_POST['reason']) ? trim($_POST['reason']) : 'Blocked by admin';
                $stmt = $conn->prepare("INSERT INTO blocked_devices (device_id, reason) VALUES (?, ?)");
                $stmt->bind_param("ss", $device_id, $reason);

                if ($stmt->execute()) {
                    $message = 'Device blocked successfully';
                    $message_type = 'success';
                } else {
                    $message = 'Error blocking device: ' . $conn->error;
                    $message_type = 'danger';
                }
            }
        }
    }

    // Unblock device
    if (isset($_POST['action']) && $_POST['action'] === 'unblock_device') {
        $device_id = isset($_POST['device_id']) ? trim($_POST['device_id']) : '';

        if (!empty($device_id)) {
            $stmt = $conn->prepare("DELETE FROM blocked_devices WHERE device_id = ?");
            $stmt->bind_param("s", $device_id);

            if ($stmt->execute()) {
                $message = 'Device unblocked successfully';
                $message_type = 'success';
            } else {
                $message = 'Error unblocking device: ' . $conn->error;
                $message_type = 'danger';
            }
        }
    }
}

// Get all devices
if (!isset($db_error)) {
    // First, check if auth_logs table exists and has data
    $check_table = $conn->query("SHOW TABLES LIKE 'auth_logs'");
    $table_exists = $check_table->num_rows > 0;

    if ($table_exists) {
        $count_result = $conn->query("SELECT COUNT(*) as count FROM auth_logs");
        $count_row = $count_result->fetch_assoc();
        $record_count = $count_row['count'];

        // Add debug message
        $message = "Debug: auth_logs table exists and contains {$record_count} records.";
        $message_type = "info";

        // Add test data if requested
        if (isset($_GET['add_test_data'])) {
            // Generate a unique device ID
            $device_id = 'TEST-DEVICE-' . rand(1000, 9999);

            // Insert a test record
            $test_sql = "INSERT INTO auth_logs
                        (license_key, pin, device_id, device_info, type, package, version, status)
                        VALUES
                        ('TEST-LICENSE', '1234', '$device_id', 'Test Device Info - Android " . rand(9, 13) . "', 'android', 'com.appystore.mrecharge', '1.0', 'success')";

            if ($conn->query($test_sql)) {
                $message .= " Added a test record with device ID $device_id successfully.";
                // Refresh the page without the parameter to avoid adding multiple test records
                header("Location: devices.php");
                exit;
            } else {
                $message .= " Failed to add test record: " . $conn->error;
                $message_type = "danger";
            }
        }
    } else {
        $message = "Debug: auth_logs table does not exist.";
        $message_type = "warning";

        // Create the table if it doesn't exist
        $create_table_sql = "CREATE TABLE IF NOT EXISTS auth_logs (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            license_key VARCHAR(255) NOT NULL,
            pin VARCHAR(50) NOT NULL,
            device_id VARCHAR(255) NOT NULL,
            device_info TEXT,
            type VARCHAR(50),
            package VARCHAR(50),
            version VARCHAR(20),
            status VARCHAR(20),
            approval_status VARCHAR(20) DEFAULT 'pending',
            approved_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        if ($conn->query($create_table_sql)) {
            $message .= " Created auth_logs table.";
        } else {
            $message .= " Failed to create auth_logs table: " . $conn->error;
            $message_type = "danger";
        }
    }

    // Create blocked_devices table if it doesn't exist
    $create_blocked_table_sql = "CREATE TABLE IF NOT EXISTS blocked_devices (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        device_id VARCHAR(255) NOT NULL UNIQUE,
        reason TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    if (!$conn->query($create_blocked_table_sql)) {
        $message .= " Failed to create blocked_devices table: " . $conn->error;
        $message_type = "danger";
    }

    // Use proper GROUP BY to show unique devices only
    $sql = "SELECT
                al.device_id,
                MAX(al.device_info) as device_info,
                MAX(al.license_key) as license_key,
                MAX(al.created_at) as last_login,
                COUNT(DISTINCT al.license_key) as license_count,
                MAX(bd.id) as is_blocked
            FROM
                auth_logs al
            LEFT JOIN
                blocked_devices bd ON al.device_id = bd.device_id
            GROUP BY
                al.device_id
            ORDER BY
                MAX(al.created_at) DESC";

    $result = $conn->query($sql);

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $devices[] = $row;
        }

        // Add more debug info
        $message .= " Query executed successfully and returned " . count($devices) . " unique devices.";
    } else {
        // Add error info
        $message .= " Query error: " . $conn->error;
        $message_type = "danger";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Device Management - AppyStore MRecharge Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/admin-style.css">
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: #f8f9fa;
        }
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .nav-link {
            font-weight: 500;
            color: #333;
        }
        .nav-link.active {
            color: #007bff;
        }
        main {
            padding-top: 48px;
        }
    </style>
</head>
<body>
    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#">AppyStore MRecharge</a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="navbar-nav">
            <div class="nav-item dropdown text-nowrap">
                <a class="nav-link dropdown-toggle px-3" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-person-circle me-1"></i>
                    <?php echo htmlspecialchars($_SESSION['admin_full_name'] ?? $_SESSION['admin_username']); ?>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                    <li><h6 class="dropdown-header">
                        <i class="bi bi-person me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['admin_full_name']); ?>
                    </h6></li>
                    <li><small class="dropdown-item-text text-muted">
                        <i class="bi bi-envelope me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['admin_email']); ?>
                    </small></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                        <i class="bi bi-key me-2"></i>Change Password
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="?logout=1" onclick="return confirm('Are you sure you want to logout?')">
                        <i class="bi bi-box-arrow-right me-2"></i>Sign Out
                    </a></li>
                </ul>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3 sidebar-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="bi bi-speedometer2 me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="licenses.php">
                                <i class="bi bi-key me-2"></i>
                                Licenses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="devices.php">
                                <i class="bi bi-phone me-2"></i>
                                Devices
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="bi bi-gear me-2"></i>
                                Settings
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <hr class="text-muted">
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                <i class="bi bi-key-fill me-2"></i>
                                Change Password
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-danger" href="?logout=1" onclick="return confirm('Are you sure you want to logout?')">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Device Management</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="devices.php?add_test_data=1" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-plus-circle"></i> Add Test Data
                        </a>
                    </div>
                </div>

                <?php if (isset($db_error)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $db_error; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?>" role="alert">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Device ID</th>
                                <th>Device Info</th>
                                <th>Current License</th>
                                <th>License Count</th>
                                <th>Last Login</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($devices)): ?>
                                <tr>
                                    <td colspan="7" class="text-center">No devices found</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($devices as $device): ?>
                                    <tr>
                                        <td><?php echo $device['device_id']; ?></td>
                                        <td><?php echo $device['device_info']; ?></td>
                                        <td><?php echo $device['license_key']; ?></td>
                                        <td><?php echo $device['license_count']; ?></td>
                                        <td><?php echo $device['last_login']; ?></td>
                                        <td>
                                            <?php if ($device['is_blocked']): ?>
                                                <span class="badge bg-danger">Blocked</span>
                                            <?php else: ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#viewHistoryModal<?php echo md5($device['device_id']); ?>" title="View History">
                                                    <i class="bi bi-clock-history"></i>
                                                </button>

                                                <?php if ($device['is_blocked']): ?>
                                                    <form method="post" class="d-inline">
                                                        <input type="hidden" name="action" value="unblock_device">
                                                        <input type="hidden" name="device_id" value="<?php echo $device['device_id']; ?>">
                                                        <button type="submit" class="btn btn-success" title="Unblock Device">
                                                            <i class="bi bi-unlock"></i>
                                                        </button>
                                                    </form>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#blockDeviceModal<?php echo md5($device['device_id']); ?>" title="Block Device">
                                                        <i class="bi bi-lock"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </main>
        </div>
    </div>

    <!-- Block Device Modals -->
    <?php foreach ($devices as $device): ?>
        <?php if (!$device['is_blocked']): ?>
            <div class="modal fade" id="blockDeviceModal<?php echo md5($device['device_id']); ?>" tabindex="-1" aria-labelledby="blockDeviceModalLabel<?php echo md5($device['device_id']); ?>" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="blockDeviceModalLabel<?php echo md5($device['device_id']); ?>">Block Device</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <form method="post">
                            <div class="modal-body">
                                <input type="hidden" name="action" value="block_device">
                                <input type="hidden" name="device_id" value="<?php echo $device['device_id']; ?>">
                                <p>Are you sure you want to block this device?</p>
                                <p><strong>Device ID:</strong> <?php echo $device['device_id']; ?></p>
                                <p><strong>Device Info:</strong> <?php echo $device['device_info']; ?></p>
                                <div class="mb-3">
                                    <label for="reason<?php echo md5($device['device_id']); ?>" class="form-label">Reason for blocking</label>
                                    <textarea class="form-control" id="reason<?php echo md5($device['device_id']); ?>" name="reason" rows="3"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-danger">Block Device</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endforeach; ?>

    <!-- View History Modals -->
    <?php foreach ($devices as $device): ?>
        <div class="modal fade" id="viewHistoryModal<?php echo md5($device['device_id']); ?>" tabindex="-1" aria-labelledby="viewHistoryModalLabel<?php echo md5($device['device_id']); ?>" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="viewHistoryModalLabel<?php echo md5($device['device_id']); ?>">Login History for Device: <?php echo $device['device_id']; ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <?php
                        // Get login history for this device
                        $history = [];
                        $stmt = $conn->prepare("SELECT * FROM auth_logs WHERE device_id = ? ORDER BY created_at DESC LIMIT 20");
                        $stmt->bind_param("s", $device['device_id']);
                        $stmt->execute();
                        $result = $stmt->get_result();

                        if ($result->num_rows > 0) {
                            while ($row = $result->fetch_assoc()) {
                                $history[] = $row;
                            }
                        }
                        ?>

                        <?php if (empty($history)): ?>
                            <p class="text-center">No login history found for this device.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Date/Time</th>
                                            <th>License Key</th>
                                            <th>PIN</th>
                                            <th>Package</th>
                                            <th>Version</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($history as $entry): ?>
                                            <tr>
                                                <td><?php echo $entry['created_at']; ?></td>
                                                <td><?php echo $entry['license_key']; ?></td>
                                                <td><?php echo $entry['pin']; ?></td>
                                                <td><?php echo $entry['package']; ?></td>
                                                <td><?php echo $entry['version']; ?></td>
                                                <td>
                                                    <?php if ($entry['status'] === 'success'): ?>
                                                        <span class="badge bg-success">Success</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Failed</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="changePasswordModalLabel">
                        <i class="bi bi-key me-2"></i>Change Password
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="dashboard.php" id="changePasswordForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="change_password">

                        <div class="mb-3">
                            <label for="current_password" class="form-label">
                                <i class="bi bi-lock me-1"></i>Current Password
                            </label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">
                                <i class="bi bi-key me-1"></i>New Password
                            </label>
                            <input type="password" class="form-control" id="new_password" name="new_password"
                                   required minlength="<?php echo ADMIN_PASSWORD_MIN_LENGTH; ?>">
                            <div class="form-text">
                                Password must be at least <?php echo ADMIN_PASSWORD_MIN_LENGTH; ?> characters long.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="bi bi-check-circle me-1"></i>Confirm New Password
                            </label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="invalid-feedback" id="password-mismatch">
                                Passwords do not match.
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Security Note:</strong> You will remain logged in after changing your password.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="changePasswordBtn">
                            <i class="bi bi-check-lg me-1"></i>Change Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
