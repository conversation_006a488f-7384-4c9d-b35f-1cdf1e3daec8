/* Admin Panel - Beautiful Color Scheme CSS */

:root {
    --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
    --secondary-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --info-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    --dark-gradient: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    --light-bg: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
    --card-bg: rgba(255, 255, 255, 0.95);
    --sidebar-bg: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: rgba(229, 231, 235, 0.8);
    --shadow-sm: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

* {
    font-family: 'Inter', sans-serif;
}

body {
    background: var(--light-bg);
    color: var(--text-primary);
    font-weight: 400;
}

/* Header Styling */
.navbar-dark {
    background: var(--dark-gradient) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-md);
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.25rem;
    background: linear-gradient(135deg, #ffffff 0%, #e5e7eb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    background: var(--sidebar-bg);
    backdrop-filter: blur(20px);
    border-right: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: 1rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.nav-link {
    font-weight: 600;
    color: var(--text-secondary);
    border-radius: 12px;
    margin: 4px 12px;
    padding: 12px 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left 0.5s;
}

.nav-link:hover {
    color: #6366f1;
    background: rgba(99, 102, 241, 0.1);
    transform: translateX(4px);
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link.active {
    color: white;
    background: var(--primary-gradient);
    box-shadow: var(--shadow-sm);
}

.nav-link.text-danger:hover {
    color: white;
    background: var(--danger-gradient);
}

/* Main Content */
main {
    padding-top: 48px;
    background: var(--light-bg);
}

/* Cards */
.card {
    border: none;
    border-radius: 20px;
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card:hover::before {
    opacity: 1;
}

/* Stat Cards */
.stat-card {
    border: none;
    border-radius: 20px;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

/* Background Colors with Gradients */
.bg-primary {
    background: var(--primary-gradient) !important;
}

.bg-success {
    background: var(--secondary-gradient) !important;
}

.bg-info {
    background: var(--info-gradient) !important;
}

.bg-warning {
    background: var(--warning-gradient) !important;
}

.bg-danger {
    background: var(--danger-gradient) !important;
}

/* Buttons */
.btn {
    border-radius: 12px;
    font-weight: 600;
    padding: 10px 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    box-shadow: var(--shadow-sm);
}

.btn-success {
    background: var(--secondary-gradient);
    box-shadow: var(--shadow-sm);
}

.btn-warning {
    background: var(--warning-gradient);
    box-shadow: var(--shadow-sm);
}

.btn-danger {
    background: var(--danger-gradient);
    box-shadow: var(--shadow-sm);
}

.btn-info {
    background: var(--info-gradient);
    box-shadow: var(--shadow-sm);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Tables */
.table {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: none;
    font-weight: 700;
    color: var(--text-primary);
    padding: 16px;
    position: relative;
}

.table thead th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-gradient);
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: rgba(99, 102, 241, 0.05);
    transform: scale(1.01);
}

/* Badges */
.badge {
    border-radius: 8px;
    font-weight: 600;
    padding: 6px 12px;
    position: relative;
    overflow: hidden;
}

.badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Modals */
.modal-content {
    border: none;
    border-radius: 20px;
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    border-radius: 20px 20px 0 0;
}

/* Forms */
.form-control, .form-select {
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 12px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus, .form-select:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    background: white;
    transform: translateY(-1px);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 16px;
    backdrop-filter: blur(20px);
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.alert-success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(22, 163, 74, 0.05) 100%);
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.alert-success::before {
    background: var(--secondary-gradient);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.alert-danger::before {
    background: var(--danger-gradient);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.alert-warning::before {
    background: var(--warning-gradient);
}

.alert-info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.05) 100%);
    color: #1d4ed8;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.alert-info::before {
    background: var(--info-gradient);
}

/* Dropdowns */
.dropdown-menu {
    border: none;
    border-radius: 16px;
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.dropdown-item {
    border-radius: 8px;
    margin: 4px 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
}

/* Pagination */
.pagination .page-link {
    border: none;
    border-radius: 12px;
    margin: 0 4px;
    font-weight: 600;
    color: var(--text-secondary);
    background: var(--card-bg);
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-sm);
}

/* Filter Card */
.filter-card {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    border: 1px solid rgba(99, 102, 241, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .stat-card:hover {
        transform: translateY(-4px);
    }
    
    .table tbody tr:hover {
        transform: none;
    }
    
    .btn:hover {
        transform: translateY(-1px);
    }
}

/* Loading Animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 2s infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
}
