# License Activation Flow Test Plan

## Overview
This document outlines the comprehensive test plan for the enhanced license activation flow with validation and UI management.

## Test Scenarios

### 1. License Activation Data Saving
**Test Case**: Verify that license activation saves all required data to SharedPreferences

**Steps**:
1. Launch the app (should show license activation UI initially)
2. Enter valid license key and PIN
3. Click "Activate" button
4. Wait for successful activation

**Expected Results**:
- License key saved to both "license_key" and "licence" preferences
- Password saved to both "password" and "pin" preferences  
- Domain saved to "domain" preference
- Expiration date saved to both "expires" and "expiration_date" preferences
- Expiration timestamp saved to "expiration_timestamp" preference
- App switches to main activity layout
- Success toast message displayed

### 2. Main Activity Field Population
**Test Case**: Verify auto-population and disabling of EditText fields

**Steps**:
1. Complete license activation (Test Case 1)
2. Observe the main activity UI

**Expected Results**:
- URL field (android:id="@+id/url") populated with domain value
- DPIN field (android:id="@+id/dpin") populated with password value
- Both fields are disabled (not editable)
- Both fields have light gray background indicating disabled state
- Fields are not focusable or clickable

### 3. Service Toggle Validation - License Expired
**Test Case**: Verify service toggle validation when license is expired

**Steps**:
1. Complete license activation
2. Manually set expiration timestamp to past date in SharedPreferences
3. Try to enable the service toggle (android:id="@+id/sav")

**Expected Results**:
- Toggle immediately reverts to OFF position
- Toast message: "Your license has expired. Please renew your subscription."
- All service requests stopped immediately
- App switches back to license activation UI
- Main app content hidden

### 4. Service Toggle Validation - Device Blocked
**Test Case**: Verify service toggle validation when device is blocked

**Steps**:
1. Complete license activation
2. Manually set device blocked status in SharedPreferences
3. Try to enable the service toggle (android:id="@+id/sav")

**Expected Results**:
- Toggle immediately reverts to OFF position
- Toast message: "Your device has been blocked. Please contact support."
- All service requests stopped immediately
- App switches back to license activation UI
- Main app content hidden

### 5. Service Toggle Validation - Both Expired and Blocked
**Test Case**: Verify service toggle validation when both conditions are true

**Steps**:
1. Complete license activation
2. Set both expired and blocked status in SharedPreferences
3. Try to enable the service toggle

**Expected Results**:
- Toggle immediately reverts to OFF position
- Toast message: "Your license has expired and device is blocked. Please reactivate your license."
- All service requests stopped immediately
- App switches back to license activation UI

### 6. Service Toggle Validation - Valid License
**Test Case**: Verify service toggle works normally with valid license

**Steps**:
1. Complete license activation with valid, non-expired license
2. Ensure device is not blocked
3. Try to enable the service toggle

**Expected Results**:
- Toggle remains in ON position
- No error messages
- Service starts normally
- App remains in main activity layout

### 7. UI Flow Management - Initial State
**Test Case**: Verify proper initial UI state

**Steps**:
1. Fresh app install (no saved activation data)
2. Launch the app

**Expected Results**:
- License activation layout visible
- Main app content hidden
- Navigation bar hidden
- Status text shows: "Please enter your license key and PIN to activate"

### 8. UI Flow Management - Activated State
**Test Case**: Verify UI state after activation

**Steps**:
1. Complete license activation
2. Observe UI state

**Expected Results**:
- License activation layout hidden
- Main app content visible
- Navigation bar visible
- EditText fields auto-populated and disabled

### 9. UI Flow Management - Return to Activation
**Test Case**: Verify UI switches back to activation on validation failure

**Steps**:
1. Complete license activation
2. Trigger validation failure (expired or blocked)
3. Observe UI state

**Expected Results**:
- License activation layout visible
- Main app content hidden
- Navigation bar hidden
- Status text reset to default message
- All monitoring stopped

### 10. Data Persistence
**Test Case**: Verify data persists across app restarts

**Steps**:
1. Complete license activation
2. Close and restart the app
3. Observe behavior

**Expected Results**:
- App shows main activity layout (not activation)
- EditText fields still populated and disabled
- All saved data intact in SharedPreferences

## Manual Testing Instructions

### Prerequisites
- Android device or emulator
- Valid license key and PIN for testing
- Access to device SharedPreferences (via ADB or debugging tools)

### Test Environment Setup
1. Install the app on test device
2. Enable developer options and USB debugging
3. Have ADB tools ready for SharedPreferences manipulation

### Validation Methods
- Use Android Studio debugger to inspect SharedPreferences
- Use ADB commands to check preference files
- Visual inspection of UI states and field population
- Log monitoring for validation messages

## Expected Log Messages
- "Auto-populated URL field with saved domain: [domain]"
- "Auto-populated DPIN field with saved password"
- "Service toggle validation failed - License expired: [true/false], Device blocked: [true/false]"
- "Service toggle validation passed - License is valid and device is not blocked"
- "Successfully switched to license activation UI"
- "Showing main app content"

## Success Criteria
All test cases must pass with expected results for the license activation flow to be considered fully functional.
