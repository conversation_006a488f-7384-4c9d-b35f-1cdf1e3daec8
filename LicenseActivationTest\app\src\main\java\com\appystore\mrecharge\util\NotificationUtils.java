package com.appystore.mrecharge.util;

import android.app.ActivityManager;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.text.Html;
import android.text.TextUtils;
import android.util.Patterns;

import androidx.core.app.NotificationCompat;
import androidx.core.content.ContextCompat;

import com.appystore.mrecharge.R;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;

public class NotificationUtils {

    private static final String TAG = NotificationUtils.class.getSimpleName();
    private static final String CHANNEL_ID = "default_channel_id";
    private static final String CHANNEL_NAME = "Default Notifications";
    private static final int SMALL_NOTIFICATION_ID = 100;
    private static final int BIG_NOTIFICATION_ID = 101;

    private final Context mContext;

    public NotificationUtils(Context context) {
        this.mContext = context;
        createNotificationChannel();
    }

    public void showNotificationMessage(String title, String message, String timestamp, Intent intent) {
        showNotificationMessage(title, message, timestamp, intent, null);
    }

    public void showNotificationMessage(String title, String message, String timestamp, Intent intent, String imageUrl) {
        if (TextUtils.isEmpty(message)) return;

        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);

        PendingIntent pendingIntent = PendingIntent.getActivity(
                mContext,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        Uri soundUri = Uri.parse("android.resource://" + mContext.getPackageName() + "/raw/notification");
        NotificationCompat.Builder builder = new NotificationCompat.Builder(mContext, CHANNEL_ID)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle(title)
                .setContentText(message)
                .setAutoCancel(true)
                .setSound(soundUri)
                .setContentIntent(pendingIntent)
                .setWhen(getTimeMilliSec(timestamp))
                .setLargeIcon(BitmapFactory.decodeResource(mContext.getResources(), R.mipmap.ic_launcher));

        if (!TextUtils.isEmpty(imageUrl) && Patterns.WEB_URL.matcher(imageUrl).matches()) {
            Bitmap image = getBitmapFromURL(imageUrl);
            if (image != null) {
                showBigNotification(image, builder, title, message);
                return;
            }
        }

        showSmallNotification(builder, title, message);
        playNotificationSound();
    }

    private void showSmallNotification(NotificationCompat.Builder builder, String title, String message) {
        NotificationCompat.InboxStyle style = new NotificationCompat.InboxStyle();
        style.addLine(message);

        builder.setStyle(style);
        NotificationManager notificationManager = ContextCompat.getSystemService(mContext, NotificationManager.class);
        if (notificationManager != null) {
            notificationManager.notify(SMALL_NOTIFICATION_ID, builder.build());
        }
    }

    private void showBigNotification(Bitmap image, NotificationCompat.Builder builder, String title, String message) {
        NotificationCompat.BigPictureStyle style = new NotificationCompat.BigPictureStyle()
                .setBigContentTitle(title)
                .setSummaryText(Html.fromHtml(message).toString())
                .bigPicture(image);

        builder.setStyle(style);
        NotificationManager notificationManager = ContextCompat.getSystemService(mContext, NotificationManager.class);
        if (notificationManager != null) {
            notificationManager.notify(BIG_NOTIFICATION_ID, builder.build());
        }
    }

    public Bitmap getBitmapFromURL(String imageUrl) {
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(imageUrl).openConnection();
            connection.setDoInput(true);
            connection.connect();
            return BitmapFactory.decodeStream(connection.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public void playNotificationSound() {
        try {
            Uri uri = Uri.parse("android.resource://" + mContext.getPackageName() + "/raw/notification");
            RingtoneManager.getRingtone(mContext, uri).play();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = mContext.getSystemService(NotificationManager.class);
            if (notificationManager != null && notificationManager.getNotificationChannel(CHANNEL_ID) == null) {
                NotificationChannel channel = new NotificationChannel(
                        CHANNEL_ID,
                        CHANNEL_NAME,
                        NotificationManager.IMPORTANCE_DEFAULT
                );
                notificationManager.createNotificationChannel(channel);
            }
        }
    }

    public static boolean isAppIsInBackground(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager == null) return true;

        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.KITKAT_WATCH) {
            return !activityManager.getRunningTasks(1).get(0).topActivity.getPackageName().equals(context.getPackageName());
        }

        for (ActivityManager.RunningAppProcessInfo processInfo : activityManager.getRunningAppProcesses()) {
            if (processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                for (String pkg : processInfo.pkgList) {
                    if (pkg.equals(context.getPackageName())) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    public static void clearNotifications(Context context) {
        NotificationManager notificationManager = ContextCompat.getSystemService(context, NotificationManager.class);
        if (notificationManager != null) {
            notificationManager.cancelAll();
        }
    }

    public static long getTimeMilliSec(String timeStamp) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return format.parse(timeStamp).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
            return 0L;
        }
    }
}
