package com.appystore.mrecharge.activity;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.appystore.mrecharge.R;

public class Settings extends AppCompatActivity {

    private static final String[] smsServers = {"Default", "Custom 1"};

    private EditText etTries, etBkash, etRocket, etNogad, etTime, etApiUrl;
    private CheckBox cbBkash, cbRocket, cbNogad;
    private Button btnSaveApiUrl;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.forward);

        SharedPreferences prefs = getSharedPreferences("serv", MODE_PRIVATE);

        Spinner spinner = findViewById(R.id.dc);
        Button btnSave = findViewById(R.id.savedata);
        LinearLayout layoutBank = findViewById(R.id.bank);
        LinearLayout layoutHome = findViewById(R.id.home);

        etTries = findViewById(R.id.trys);
        etBkash = findViewById(R.id.bkash);
        etRocket = findViewById(R.id.rocket);
        etNogad = findViewById(R.id.nogad);
        etTime = findViewById(R.id.time);
        etApiUrl = findViewById(R.id.api_url);
        btnSaveApiUrl = findViewById(R.id.save_api_url);

        cbBkash = findViewById(R.id.bks);
        cbRocket = findViewById(R.id.rks);
        cbNogad = findViewById(R.id.ngs);

        // Spinner setup
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, smsServers);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinner.setAdapter(adapter);
        spinner.setSelection(prefs.getInt("dc", 0));

        // Load saved preferences
        etBkash.setText(getPref("bkash", "01", this));
        etRocket.setText(getPref("rocket", "01", this));
        etNogad.setText(getPref("nogad", "01", this));
        etTime.setText(getPref("stime", "5", this));
        etTries.setText(String.valueOf(prefs.getInt("trys", 2)));

        // Load API URL
        String apiUrl = getPref("api_url", "http://192.168.0.106/AppyStoreMRecharge/admin/api/device_auth.php", this);
        etApiUrl.setText(apiUrl);

        cbBkash.setChecked(getPref("bkw", "", this).contains("BK"));
        cbRocket.setChecked(getPref("rkw", "", this).contains("RK"));
        cbNogad.setChecked(getPref("ngw", "", this).contains("NG"));

        // Save button
        btnSave.setOnClickListener(view -> {
            SavePreferences("bkash", etBkash.getText().toString());
            SavePreferences("rocket", etRocket.getText().toString());
            SavePreferences("nogad", etNogad.getText().toString());
            SavePreferences("stime", etTime.getText().toString());

            try {
                int tries = Integer.parseInt(etTries.getText().toString());
                prefs.edit().putInt("trys", tries).apply();
            } catch (NumberFormatException e) {
                Toast.makeText(this, "Invalid number for tries", Toast.LENGTH_SHORT).show();
                return;
            }

            Toast.makeText(this, "Saved", Toast.LENGTH_SHORT).show();
        });

        // Spinner selection listener
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                prefs.edit().putInt("dc", position).apply();
            }
            @Override public void onNothingSelected(AdapterView<?> parent) {}
        });

        // Navigation buttons
        layoutHome.setOnClickListener(v -> startActivity(new Intent(this, MainActivity.class)));
        layoutBank.setOnClickListener(v -> startActivity(new Intent(this, intsetting.class)));

        // API URL save button
        btnSaveApiUrl.setOnClickListener(v -> {
            String newApiUrl = etApiUrl.getText().toString().trim();
            if (!newApiUrl.isEmpty()) {
                try {
                    // Validate URL format
                    new java.net.URL(newApiUrl);

                    // Save the API URL
                    SavePreferences("api_url", newApiUrl);
                    Toast.makeText(this, "API URL updated", Toast.LENGTH_SHORT).show();
                } catch (java.net.MalformedURLException e) {
                    Toast.makeText(this, "Invalid URL format", Toast.LENGTH_LONG).show();
                }
            } else {
                Toast.makeText(this, "API URL cannot be empty", Toast.LENGTH_SHORT).show();
            }
        });
    }

    public void checkbox_clicked(View view) {
        boolean checked = ((CheckBox) view).isChecked();
        int id = view.getId();
        if (id == R.id.bks) {
            SavePreferences("bkw", checked ? "BK" : "no");
        } else if (id == R.id.rks) {
            SavePreferences("rkw", checked ? "RK" : "no");
        } else if (id == R.id.ngs) {
            SavePreferences("ngw", checked ? "NG" : "no");
        }
    }

    private void SavePreferences(String key, String value) {
        SharedPreferences.Editor editor = PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit();
        editor.putString(key, value);
        editor.apply();
    }

    public static String getPref(String key, String defaultVal, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(key, defaultVal);
    }
}
