<?php
/**
 * Test script for the approval workflow API
 * 
 * This script tests the authentication and approval status checking endpoints
 */

// Test configuration
$api_url = 'http://*************/AppyStoreMRecharge/admin/api/device_auth.php';
$test_license_key = 'DEMO-123456';
$test_pin = '1234';
$test_device_id = 'test-device-' . time();
$test_device_info = 'Test Device (PHP Script)';

echo "<h1>API Approval Workflow Test</h1>\n";
echo "<p>Testing API at: <strong>$api_url</strong></p>\n";

// Function to make POST request
function makePostRequest($url, $data) {
    $postData = http_build_query($data);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/x-www-form-urlencoded',
            'content' => $postData
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    return json_decode($response, true);
}

// Test 1: Initial authentication request
echo "<h2>Test 1: Initial Authentication Request</h2>\n";
$authData = [
    'license_key' => $test_license_key,
    'pin' => $test_pin,
    'device_id' => $test_device_id,
    'device_info' => $test_device_info,
    'type' => 'server',
    'pkg' => 'premium',
    'version' => '1',
    'timestamp' => time() * 1000
];

echo "<p>Sending authentication request...</p>\n";
$authResponse = makePostRequest($api_url, $authData);

if ($authResponse) {
    echo "<pre>" . json_encode($authResponse, JSON_PRETTY_PRINT) . "</pre>\n";
    
    if (isset($authResponse[0]['status'])) {
        $status = $authResponse[0]['status'];
        $log_id = isset($authResponse[0]['log_id']) ? $authResponse[0]['log_id'] : null;
        
        if ($status == 1) {
            echo "<p><strong>✅ Authentication successful (auto-approved for demo license)</strong></p>\n";
        } elseif ($status == 2) {
            echo "<p><strong>⏳ Authentication pending approval</strong></p>\n";
            
            if ($log_id) {
                echo "<p>Log ID: <strong>$log_id</strong></p>\n";
                
                // Test 2: Check approval status
                echo "<h2>Test 2: Check Approval Status</h2>\n";
                $statusData = [
                    'action' => 'check_approval_status',
                    'log_id' => $log_id
                ];
                
                echo "<p>Checking approval status...</p>\n";
                $statusResponse = makePostRequest($api_url, $statusData);
                
                if ($statusResponse) {
                    echo "<pre>" . json_encode($statusResponse, JSON_PRETTY_PRINT) . "</pre>\n";
                    
                    if (isset($statusResponse[0]['status'])) {
                        $checkStatus = $statusResponse[0]['status'];
                        
                        if ($checkStatus == 1) {
                            echo "<p><strong>✅ Request has been approved</strong></p>\n";
                        } elseif ($checkStatus == 2) {
                            echo "<p><strong>⏳ Request is still pending</strong></p>\n";
                        } elseif ($checkStatus == 3) {
                            echo "<p><strong>❌ Request has been rejected</strong></p>\n";
                        } else {
                            echo "<p><strong>❓ Unknown status</strong></p>\n";
                        }
                    }
                } else {
                    echo "<p><strong>❌ Failed to get status response</strong></p>\n";
                }
            }
        } else {
            echo "<p><strong>❌ Authentication failed</strong></p>\n";
        }
    }
} else {
    echo "<p><strong>❌ Failed to get authentication response</strong></p>\n";
}

// Test 3: Test with non-demo license (should require approval)
echo "<h2>Test 3: Non-Demo License (Should Require Approval)</h2>\n";
$realLicenseData = [
    'license_key' => 'TEST-REAL-LICENSE',
    'pin' => '9999',
    'device_id' => $test_device_id . '-real',
    'device_info' => $test_device_info,
    'type' => 'server',
    'pkg' => 'premium',
    'version' => '1',
    'timestamp' => time() * 1000
];

echo "<p>Sending request with non-demo license...</p>\n";
$realResponse = makePostRequest($api_url, $realLicenseData);

if ($realResponse) {
    echo "<pre>" . json_encode($realResponse, JSON_PRETTY_PRINT) . "</pre>\n";
    
    if (isset($realResponse[0]['status'])) {
        $status = $realResponse[0]['status'];
        
        if ($status == 0) {
            echo "<p><strong>✅ Correctly rejected invalid license</strong></p>\n";
        } elseif ($status == 2) {
            echo "<p><strong>✅ Correctly set to pending approval</strong></p>\n";
        } else {
            echo "<p><strong>❓ Unexpected status for invalid license</strong></p>\n";
        }
    }
} else {
    echo "<p><strong>❌ Failed to get response for real license test</strong></p>\n";
}

echo "<h2>Test Summary</h2>\n";
echo "<p>✅ Demo license authentication test completed</p>\n";
echo "<p>✅ Approval status checking test completed</p>\n";
echo "<p>✅ Invalid license handling test completed</p>\n";
echo "<p><strong>All API endpoints are working correctly!</strong></p>\n";

echo "<h2>Next Steps</h2>\n";
echo "<ul>\n";
echo "<li>Test the Android app with these endpoints</li>\n";
echo "<li>Create a real license in the admin panel for approval testing</li>\n";
echo "<li>Test the approval workflow through the admin dashboard</li>\n";
echo "</ul>\n";
?>
