<?php
/**
 * Test script for license expiration functionality
 * 
 * This script creates test licenses with different expiration dates
 * to test the expiration management system
 */

// Database connection details
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'appystore_mrecharge';

// Connect to database
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>License Expiration Test Setup</h1>\n";

// Create test licenses with different expiration scenarios
$test_licenses = [
    [
        'license_key' => 'TEST-EXPIRED',
        'pin' => '1111',
        'package' => 'test',
        'domain' => '*************',
        'max_devices' => 1,
        'expires_at' => date('Y-m-d', strtotime('-1 day')) // Already expired
    ],
    [
        'license_key' => 'TEST-EXPIRING-SOON',
        'pin' => '2222',
        'package' => 'test',
        'domain' => '*************',
        'max_devices' => 1,
        'expires_at' => date('Y-m-d', strtotime('+1 hour')) // Expires in 1 hour
    ],
    [
        'license_key' => 'TEST-WARNING-PERIOD',
        'pin' => '3333',
        'package' => 'test',
        'domain' => '*************',
        'max_devices' => 1,
        'expires_at' => date('Y-m-d', strtotime('+1 day')) // Expires in 1 day (warning period)
    ],
    [
        'license_key' => 'TEST-VALID-LONG',
        'pin' => '4444',
        'package' => 'test',
        'domain' => '*************',
        'max_devices' => 1,
        'expires_at' => date('Y-m-d', strtotime('+30 days')) // Valid for 30 days
    ]
];

echo "<h2>Creating Test Licenses</h2>\n";

foreach ($test_licenses as $license) {
    // Check if license already exists
    $stmt = $conn->prepare("SELECT id FROM licenses WHERE license_key = ?");
    $stmt->bind_param("s", $license['license_key']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Update existing license
        $stmt = $conn->prepare("UPDATE licenses SET pin = ?, package = ?, domain = ?, max_devices = ?, expires_at = ? WHERE license_key = ?");
        $stmt->bind_param("sssiss", 
            $license['pin'], 
            $license['package'], 
            $license['domain'], 
            $license['max_devices'], 
            $license['expires_at'], 
            $license['license_key']
        );
        
        if ($stmt->execute()) {
            echo "<p>✅ Updated license: <strong>{$license['license_key']}</strong> (PIN: {$license['pin']}) - Expires: {$license['expires_at']}</p>\n";
        } else {
            echo "<p>❌ Failed to update license: {$license['license_key']}</p>\n";
        }
    } else {
        // Insert new license
        $stmt = $conn->prepare("INSERT INTO licenses (license_key, pin, package, domain, max_devices, expires_at) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("ssssis", 
            $license['license_key'], 
            $license['pin'], 
            $license['package'], 
            $license['domain'], 
            $license['max_devices'], 
            $license['expires_at']
        );
        
        if ($stmt->execute()) {
            echo "<p>✅ Created license: <strong>{$license['license_key']}</strong> (PIN: {$license['pin']}) - Expires: {$license['expires_at']}</p>\n";
        } else {
            echo "<p>❌ Failed to create license: {$license['license_key']}</p>\n";
        }
    }
}

echo "<h2>Test Scenarios</h2>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<h3>📱 Android App Testing Instructions:</h3>\n";
echo "<ol>\n";
echo "<li><strong>Already Expired License:</strong><br>\n";
echo "   License: <code>TEST-EXPIRED</code>, PIN: <code>1111</code><br>\n";
echo "   Expected: Should immediately show expiration dialog and logout</li>\n";
echo "<li><strong>Expiring Soon (1 hour):</strong><br>\n";
echo "   License: <code>TEST-EXPIRING-SOON</code>, PIN: <code>2222</code><br>\n";
echo "   Expected: Should show red warnings every 5 minutes, non-dismissible dialogs</li>\n";
echo "<li><strong>Warning Period (1 day):</strong><br>\n";
echo "   License: <code>TEST-WARNING-PERIOD</code>, PIN: <code>3333</code><br>\n";
echo "   Expected: Should show orange warnings every 5 minutes</li>\n";
echo "<li><strong>Valid License (30 days):</strong><br>\n";
echo "   License: <code>TEST-VALID-LONG</code>, PIN: <code>4444</code><br>\n";
echo "   Expected: Should show green status, no warnings</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<h2>Testing Features</h2>\n";
echo "<ul>\n";
echo "<li>✅ Real-time countdown display</li>\n";
echo "<li>✅ Color-coded status (Green → Orange → Red)</li>\n";
echo "<li>✅ Progress bar visualization</li>\n";
echo "<li>✅ System notifications every 5 minutes during warning period</li>\n";
echo "<li>✅ In-app warning dialogs</li>\n";
echo "<li>✅ Non-dismissible warnings during final 24 hours</li>\n";
echo "<li>✅ Automatic logout on expiration</li>\n";
echo "<li>✅ Clear authentication data on expiration</li>\n";
echo "<li>✅ Background monitoring continues when app is minimized</li>\n";
echo "</ul>\n";

echo "<h2>Quick Test for Immediate Expiration</h2>\n";
echo "<p>To test immediate expiration, you can temporarily modify the expiration time:</p>\n";
echo "<form method='post' style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<label>Set license to expire in: </label>\n";
echo "<select name='expire_in'>\n";
echo "<option value='1'>1 minute</option>\n";
echo "<option value='5'>5 minutes</option>\n";
echo "<option value='10'>10 minutes</option>\n";
echo "<option value='30'>30 minutes</option>\n";
echo "</select>\n";
echo "<select name='license_key'>\n";
foreach ($test_licenses as $license) {
    echo "<option value='{$license['license_key']}'>{$license['license_key']}</option>\n";
}
echo "</select>\n";
echo "<input type='submit' name='set_expiration' value='Set Expiration' style='margin-left: 10px; padding: 5px 10px;'>\n";
echo "</form>\n";

// Handle quick expiration setting
if (isset($_POST['set_expiration'])) {
    $expire_in = (int)$_POST['expire_in'];
    $license_key = $_POST['license_key'];
    
    $new_expiration = date('Y-m-d H:i:s', strtotime("+{$expire_in} minutes"));
    
    $stmt = $conn->prepare("UPDATE licenses SET expires_at = ? WHERE license_key = ?");
    $stmt->bind_param("ss", $new_expiration, $license_key);
    
    if ($stmt->execute()) {
        echo "<div style='background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>\n";
        echo "✅ License <strong>$license_key</strong> set to expire at: <strong>$new_expiration</strong>\n";
        echo "</div>\n";
    }
}

echo "<h2>Current Test Licenses</h2>\n";
echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
echo "<tr style='background: #f8f9fa;'>\n";
echo "<th style='padding: 8px;'>License Key</th>\n";
echo "<th style='padding: 8px;'>PIN</th>\n";
echo "<th style='padding: 8px;'>Expires At</th>\n";
echo "<th style='padding: 8px;'>Status</th>\n";
echo "<th style='padding: 8px;'>Time Remaining</th>\n";
echo "</tr>\n";

$result = $conn->query("SELECT * FROM licenses WHERE license_key LIKE 'TEST-%' ORDER BY expires_at");
while ($row = $result->fetch_assoc()) {
    $expires_at = strtotime($row['expires_at'] . ' 23:59:59');
    $current_time = time();
    $time_remaining = $expires_at - $current_time;
    
    $status_color = '#28a745'; // Green
    $status_text = 'Valid';
    
    if ($time_remaining <= 0) {
        $status_color = '#dc3545'; // Red
        $status_text = 'Expired';
        $time_remaining_text = 'Expired';
    } elseif ($time_remaining <= 24 * 60 * 60) {
        $status_color = '#dc3545'; // Red
        $status_text = 'Critical';
        $hours = floor($time_remaining / 3600);
        $minutes = floor(($time_remaining % 3600) / 60);
        $time_remaining_text = "{$hours}h {$minutes}m";
    } elseif ($time_remaining <= 48 * 60 * 60) {
        $status_color = '#fd7e14'; // Orange
        $status_text = 'Warning';
        $hours = floor($time_remaining / 3600);
        $time_remaining_text = "{$hours} hours";
    } else {
        $days = floor($time_remaining / (24 * 60 * 60));
        $time_remaining_text = "{$days} days";
    }
    
    echo "<tr>\n";
    echo "<td style='padding: 8px;'><strong>{$row['license_key']}</strong></td>\n";
    echo "<td style='padding: 8px;'>{$row['pin']}</td>\n";
    echo "<td style='padding: 8px;'>{$row['expires_at']}</td>\n";
    echo "<td style='padding: 8px; color: $status_color; font-weight: bold;'>$status_text</td>\n";
    echo "<td style='padding: 8px;'>$time_remaining_text</td>\n";
    echo "</tr>\n";
}

echo "</table>\n";

$conn->close();
?>
