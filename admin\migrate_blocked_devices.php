<?php
/**
 * Database Migration Script for blocked_devices Table
 * 
 * This script ensures the blocked_devices table has the correct structure
 * with all required columns for the device blocking functionality.
 */

// Database connection details
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'appystore_mrecharge';

echo "<h1>Blocked Devices Table Migration</h1>\n";
echo "<p>Ensuring blocked_devices table has correct structure...</p>\n";

// Connect to database
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

// Check connection
if ($conn->connect_error) {
    die("<p style='color: red;'>❌ Database connection failed: " . $conn->connect_error . "</p>\n");
}

echo "<p style='color: green;'>✅ Database connection successful</p>\n";

// Step 1: Create blocked_devices table if it doesn't exist
echo "<h2>Step 1: Creating blocked_devices table</h2>\n";

$create_table_sql = "CREATE TABLE IF NOT EXISTS blocked_devices (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(255) NOT NULL UNIQUE,
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($create_table_sql)) {
    echo "<p style='color: green;'>✅ blocked_devices table created/verified</p>\n";
} else {
    echo "<p style='color: red;'>❌ Error creating table: " . $conn->error . "</p>\n";
    exit;
}

// Step 2: Check and add missing columns
echo "<h2>Step 2: Checking table structure</h2>\n";

// Check if created_at column exists
$check_created_at = $conn->query("SHOW COLUMNS FROM blocked_devices LIKE 'created_at'");
if ($check_created_at->num_rows === 0) {
    echo "<p style='color: yellow;'>⚠️ created_at column missing, adding...</p>\n";
    
    if ($conn->query("ALTER TABLE blocked_devices ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")) {
        echo "<p style='color: green;'>✅ created_at column added successfully</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Error adding created_at column: " . $conn->error . "</p>\n";
    }
} else {
    echo "<p style='color: green;'>✅ created_at column exists</p>\n";
}

// Check if reason column exists
$check_reason = $conn->query("SHOW COLUMNS FROM blocked_devices LIKE 'reason'");
if ($check_reason->num_rows === 0) {
    echo "<p style='color: yellow;'>⚠️ reason column missing, adding...</p>\n";
    
    if ($conn->query("ALTER TABLE blocked_devices ADD COLUMN reason TEXT AFTER device_id")) {
        echo "<p style='color: green;'>✅ reason column added successfully</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Error adding reason column: " . $conn->error . "</p>\n";
    }
} else {
    echo "<p style='color: green;'>✅ reason column exists</p>\n";
}

// Step 3: Verify table structure
echo "<h2>Step 3: Final table structure verification</h2>\n";

$structure_query = $conn->query("DESCRIBE blocked_devices");
if ($structure_query) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr style='background-color: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
    
    while ($row = $structure_query->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<p style='color: green;'>✅ Table structure verified successfully</p>\n";
} else {
    echo "<p style='color: red;'>❌ Error checking table structure: " . $conn->error . "</p>\n";
}

// Step 4: Test the blocking functionality
echo "<h2>Step 4: Testing blocking functionality</h2>\n";

$test_device_id = 'MIGRATION-TEST-' . time();
$test_reason = 'Migration test - safe to delete';

// Test insert
$stmt = $conn->prepare("INSERT INTO blocked_devices (device_id, reason) VALUES (?, ?)");
if ($stmt) {
    $stmt->bind_param("ss", $test_device_id, $test_reason);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ Test insert successful</p>\n";
        
        // Test select
        $select_stmt = $conn->prepare("SELECT device_id, reason, created_at FROM blocked_devices WHERE device_id = ?");
        if ($select_stmt) {
            $select_stmt->bind_param("s", $test_device_id);
            $select_stmt->execute();
            $result = $select_stmt->get_result();
            
            if ($result->num_rows > 0) {
                $row = $result->fetch_assoc();
                echo "<p style='color: green;'>✅ Test select successful</p>\n";
                echo "<p>Test data: Device ID: " . htmlspecialchars($row['device_id']) . 
                     ", Reason: " . htmlspecialchars($row['reason']) . 
                     ", Created: " . htmlspecialchars($row['created_at']) . "</p>\n";
                
                // Clean up test data
                $delete_stmt = $conn->prepare("DELETE FROM blocked_devices WHERE device_id = ?");
                if ($delete_stmt) {
                    $delete_stmt->bind_param("s", $test_device_id);
                    if ($delete_stmt->execute()) {
                        echo "<p style='color: green;'>✅ Test cleanup successful</p>\n";
                    }
                    $delete_stmt->close();
                }
            } else {
                echo "<p style='color: red;'>❌ Test select failed - no data found</p>\n";
            }
            $select_stmt->close();
        }
    } else {
        echo "<p style='color: red;'>❌ Test insert failed: " . $stmt->error . "</p>\n";
    }
    $stmt->close();
} else {
    echo "<p style='color: red;'>❌ Error preparing test statement: " . $conn->error . "</p>\n";
}

// Step 5: Summary
echo "<h2>Migration Summary</h2>\n";
echo "<div style='background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<h3 style='color: green; margin-top: 0;'>✅ Migration Completed Successfully</h3>\n";
echo "<p><strong>The blocked_devices table is now ready for use with the following features:</strong></p>\n";
echo "<ul>\n";
echo "<li>✅ Device blocking and unblocking functionality</li>\n";
echo "<li>✅ Block reason storage and retrieval</li>\n";
echo "<li>✅ Timestamp tracking for when devices were blocked</li>\n";
echo "<li>✅ Integration with device authentication API</li>\n";
echo "<li>✅ Automatic logout functionality for blocked devices</li>\n";
echo "</ul>\n";
echo "<p><strong>Next steps:</strong></p>\n";
echo "<ol>\n";
echo "<li>Test the device blocking functionality in the admin panel</li>\n";
echo "<li>Verify automatic logout works in the Android application</li>\n";
echo "<li>Check that block reasons are displayed correctly</li>\n";
echo "</ol>\n";
echo "</div>\n";

$conn->close();

echo "<p style='color: blue;'><strong>Migration completed at:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
