/**
 * Test class to demonstrate the JSON parsing fix
 * This shows how the parseJsonResponse() method handles different response formats
 */
public class JsonParsingTest {
    
    /**
     * Test the parseJsonResponse method with different JSON formats
     */
    public static void testJsonParsing() {
        
        // Test Case 1: JSON Array format (current API response)
        String arrayResponse = "[{\"status\":0,\"version\":1,\"message\":\"Invalid license key\"}]";
        System.out.println("Testing Array Response: " + arrayResponse);
        try {
            JSONObject result = parseJsonResponse(arrayResponse);
            System.out.println("✅ Successfully parsed array response");
            System.out.println("Status: " + result.getInt("status"));
            System.out.println("Message: " + result.getString("message"));
        } catch (JSONException e) {
            System.out.println("❌ Failed to parse array response: " + e.getMessage());
        }
        
        // Test Case 2: JSON Object format (direct object)
        String objectResponse = "{\"status\":1,\"version\":1,\"message\":\"License valid\"}";
        System.out.println("\nTesting Object Response: " + objectResponse);
        try {
            JSONObject result = parseJsonResponse(objectResponse);
            System.out.println("✅ Successfully parsed object response");
            System.out.println("Status: " + result.getInt("status"));
            System.out.println("Message: " + result.getString("message"));
        } catch (JSONException e) {
            System.out.println("❌ Failed to parse object response: " + e.getMessage());
        }
        
        // Test Case 3: Wrapped response format
        String wrappedResponse = "{\"success\":true,\"data\":{\"status\":1,\"is_blocked\":false,\"expiration_time\":**********}}";
        System.out.println("\nTesting Wrapped Response: " + wrappedResponse);
        try {
            JSONObject result = parseJsonResponse(wrappedResponse);
            System.out.println("✅ Successfully parsed wrapped response");
            System.out.println("Success: " + result.getBoolean("success"));
            if (result.has("data")) {
                JSONObject data = result.getJSONObject("data");
                System.out.println("Data Status: " + data.getInt("status"));
                System.out.println("Is Blocked: " + data.getBoolean("is_blocked"));
            }
        } catch (JSONException e) {
            System.out.println("❌ Failed to parse wrapped response: " + e.getMessage());
        }
        
        // Test Case 4: Empty array (error case)
        String emptyArrayResponse = "[]";
        System.out.println("\nTesting Empty Array Response: " + emptyArrayResponse);
        try {
            JSONObject result = parseJsonResponse(emptyArrayResponse);
            System.out.println("❌ Should have failed for empty array");
        } catch (JSONException e) {
            System.out.println("✅ Correctly handled empty array: " + e.getMessage());
        }
        
        // Test Case 5: Invalid JSON (error case)
        String invalidResponse = "invalid json";
        System.out.println("\nTesting Invalid JSON Response: " + invalidResponse);
        try {
            JSONObject result = parseJsonResponse(invalidResponse);
            System.out.println("❌ Should have failed for invalid JSON");
        } catch (JSONException e) {
            System.out.println("✅ Correctly handled invalid JSON: " + e.getMessage());
        }
        
        // Test Case 6: Null response (error case)
        String nullResponse = null;
        System.out.println("\nTesting Null Response: " + nullResponse);
        try {
            JSONObject result = parseJsonResponse(nullResponse);
            System.out.println("❌ Should have failed for null response");
        } catch (JSONException e) {
            System.out.println("✅ Correctly handled null response: " + e.getMessage());
        }
    }
    
    /**
     * Copy of the parseJsonResponse method from MainActivity for testing
     */
    private static JSONObject parseJsonResponse(String response) throws JSONException {
        if (response == null || response.trim().isEmpty()) {
            throw new JSONException("Empty or null response");
        }
        
        String trimmedResponse = response.trim();
        
        try {
            // First, try to parse as JSONArray (most common case for this API)
            if (trimmedResponse.startsWith("[")) {
                JSONArray jsonArray = new JSONArray(trimmedResponse);
                if (jsonArray.length() > 0) {
                    return jsonArray.getJSONObject(0);
                } else {
                    throw new JSONException("Empty JSON array received");
                }
            }
            // If not an array, try to parse as JSONObject
            else if (trimmedResponse.startsWith("{")) {
                return new JSONObject(trimmedResponse);
            }
            // If neither array nor object format, throw exception
            else {
                throw new JSONException("Response is neither JSON array nor object: " + trimmedResponse);
            }
        } catch (JSONException e) {
            throw new JSONException("Invalid JSON format: " + e.getMessage());
        }
    }
    
    /**
     * Main method to run the tests
     */
    public static void main(String[] args) {
        System.out.println("=== JSON Parsing Fix Test ===");
        testJsonParsing();
        System.out.println("\n=== Test Complete ===");
    }
}
