<?php
/**
 * Admin Panel Configuration File
 * 
 * This file contains all configuration settings for the admin panel.
 * Update the settings below according to your environment.
 */

// Prevent direct access
if (!defined('ADMIN_ACCESS')) {
    die('Direct access not allowed');
}

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'appystore_mrecharge');

// Admin Panel Configuration
define('ADMIN_PANEL_TITLE', 'AppyStore MRecharge Admin');
define('ADMIN_PANEL_VERSION', '2.0');
define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hour in seconds

// Security Configuration
define('ADMIN_PASSWORD_MIN_LENGTH', 6);
define('ADMIN_MAX_LOGIN_ATTEMPTS', 5);
define('ADMIN_LOCKOUT_TIME', 900); // 15 minutes in seconds

// Application Settings
define('DEFAULT_LICENSE_DURATION_DAYS', 365);
define('DEFAULT_MAX_DEVICES_PER_LICENSE', 5);
define('DEFAULT_DOMAIN', 'appystore.com');

// API Configuration
define('API_VERSION', '1.0');
define('API_TIMEOUT', 30); // seconds

// File Upload Settings
define('MAX_UPLOAD_SIZE', 5242880); // 5MB in bytes
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']);

// Email Configuration (for future use)
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'AppyStore Admin');

// Timezone Configuration
define('DEFAULT_TIMEZONE', 'Asia/Dhaka');
date_default_timezone_set(DEFAULT_TIMEZONE);

// Error Reporting (set to 0 in production)
define('DEBUG_MODE', 1);
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Database Connection Function
function getDbConnection() {
    static $conn = null;
    
    if ($conn === null) {
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        
        if ($conn->connect_error) {
            if (DEBUG_MODE) {
                die("Database connection failed: " . $conn->connect_error);
            } else {
                die("Database connection failed. Please contact administrator.");
            }
        }
        
        // Set charset to UTF-8
        $conn->set_charset("utf8");
    }
    
    return $conn;
}

// Initialize Admin Users Table
function initializeAdminUsersTable() {
    $conn = getDbConnection();
    
    // Create admin_users table if it doesn't exist
    $create_table_sql = "CREATE TABLE IF NOT EXISTS admin_users (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        role ENUM('admin', 'moderator') DEFAULT 'admin',
        status ENUM('active', 'inactive') DEFAULT 'active',
        last_login TIMESTAMP NULL,
        login_attempts INT(11) DEFAULT 0,
        locked_until TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($create_table_sql)) {
        // Check if default admin user exists
        $check_admin = $conn->query("SELECT id FROM admin_users WHERE username = 'admin'");
        
        if ($check_admin->num_rows === 0) {
            // Create default admin user with plain text password
            $default_password = 'admin123'; // This will be stored as plain text
            $insert_admin = "INSERT INTO admin_users (username, password, full_name, email, role) 
                           VALUES ('admin', '$default_password', 'System Administrator', '<EMAIL>', 'admin')";
            
            if ($conn->query($insert_admin)) {
                error_log("Default admin user created successfully");
            } else {
                error_log("Error creating default admin user: " . $conn->error);
            }
        }
    } else {
        error_log("Error creating admin_users table: " . $conn->error);
    }
}

// Authentication Functions
function authenticateUser($username, $password) {
    $conn = getDbConnection();
    
    // Check if user is locked
    $check_lock_sql = "SELECT locked_until FROM admin_users WHERE username = ? AND locked_until > NOW()";
    $stmt = $conn->prepare($check_lock_sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        return ['success' => false, 'message' => 'Account is temporarily locked. Please try again later.'];
    }
    
    // Get user data
    $sql = "SELECT id, username, password, full_name, email, role, status, login_attempts FROM admin_users WHERE username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return ['success' => false, 'message' => 'Invalid username or password'];
    }
    
    $user = $result->fetch_assoc();
    
    // Check if account is active
    if ($user['status'] !== 'active') {
        return ['success' => false, 'message' => 'Account is inactive'];
    }
    
    // Verify password (plain text comparison)
    if ($password === $user['password']) {
        // Reset login attempts and update last login
        $update_sql = "UPDATE admin_users SET login_attempts = 0, last_login = NOW(), locked_until = NULL WHERE id = ?";
        $stmt = $conn->prepare($update_sql);
        $stmt->bind_param("i", $user['id']);
        $stmt->execute();
        
        return [
            'success' => true,
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'full_name' => $user['full_name'],
                'email' => $user['email'],
                'role' => $user['role']
            ]
        ];
    } else {
        // Increment login attempts
        $attempts = $user['login_attempts'] + 1;
        $locked_until = null;
        
        if ($attempts >= ADMIN_MAX_LOGIN_ATTEMPTS) {
            $locked_until = date('Y-m-d H:i:s', time() + ADMIN_LOCKOUT_TIME);
        }
        
        $update_sql = "UPDATE admin_users SET login_attempts = ?, locked_until = ? WHERE id = ?";
        $stmt = $conn->prepare($update_sql);
        $stmt->bind_param("isi", $attempts, $locked_until, $user['id']);
        $stmt->execute();
        
        $remaining_attempts = ADMIN_MAX_LOGIN_ATTEMPTS - $attempts;
        if ($remaining_attempts > 0) {
            return ['success' => false, 'message' => "Invalid username or password. $remaining_attempts attempts remaining."];
        } else {
            return ['success' => false, 'message' => 'Account locked due to too many failed attempts. Please try again later.'];
        }
    }
}

function changeUserPassword($user_id, $current_password, $new_password) {
    $conn = getDbConnection();
    
    // Validate new password length
    if (strlen($new_password) < ADMIN_PASSWORD_MIN_LENGTH) {
        return ['success' => false, 'message' => 'New password must be at least ' . ADMIN_PASSWORD_MIN_LENGTH . ' characters long'];
    }
    
    // Get current password
    $sql = "SELECT password FROM admin_users WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return ['success' => false, 'message' => 'User not found'];
    }
    
    $user = $result->fetch_assoc();
    
    // Verify current password (plain text comparison)
    if ($current_password !== $user['password']) {
        return ['success' => false, 'message' => 'Current password is incorrect'];
    }
    
    // Update password (store as plain text)
    $update_sql = "UPDATE admin_users SET password = ?, updated_at = NOW() WHERE id = ?";
    $stmt = $conn->prepare($update_sql);
    $stmt->bind_param("si", $new_password, $user_id);
    
    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'Password changed successfully'];
    } else {
        return ['success' => false, 'message' => 'Error updating password'];
    }
}

// Session Management
function startAdminSession($user_data) {
    session_start();
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_user_id'] = $user_data['id'];
    $_SESSION['admin_username'] = $user_data['username'];
    $_SESSION['admin_full_name'] = $user_data['full_name'];
    $_SESSION['admin_email'] = $user_data['email'];
    $_SESSION['admin_role'] = $user_data['role'];
    $_SESSION['admin_login_time'] = time();
}

function checkAdminSession() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        return false;
    }
    
    // Check session timeout
    if (isset($_SESSION['admin_login_time']) && (time() - $_SESSION['admin_login_time']) > ADMIN_SESSION_TIMEOUT) {
        destroyAdminSession();
        return false;
    }
    
    return true;
}

function destroyAdminSession() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    $_SESSION = array();
    session_destroy();
}

// Utility Functions
function formatDateTime($datetime) {
    return date('M d, Y h:i A', strtotime($datetime));
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

// Initialize the admin users table when config is loaded
initializeAdminUsersTable();

?>
