<?php
/**
 * Admin Panel - License Management
 *
 * This file handles license management functionality.
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include configuration
require_once 'config.php';

// Check if user is logged in
if (!checkAdminSession()) {
    header('Location: login.php');
    exit;
}

// Get database connection
$conn = getDbConnection();
$db_error = null;

// Initialize variables
$licenses = [];
$message = '';
$message_type = '';

// Create licenses table if it doesn't exist
if (!isset($db_error)) {
    $create_table_sql = "CREATE TABLE IF NOT EXISTS licenses (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        license_key VARCHAR(255) NOT NULL UNIQUE,
        pin VARCHAR(50) NOT NULL,
        package VARCHAR(50) NOT NULL,
        status TINYINT(1) DEFAULT 1,
        max_devices INT(11) DEFAULT 1,
        expires_at DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    $conn->query($create_table_sql);

    // Check if domain column exists, if not add it
    $check_domain_column = "SHOW COLUMNS FROM licenses LIKE 'domain'";
    $result = $conn->query($check_domain_column);

    if ($result && $result->num_rows == 0) {
        // Domain column doesn't exist, add it
        $add_domain_column = "ALTER TABLE licenses ADD COLUMN domain VARCHAR(255) DEFAULT 'appystore.com' AFTER package";
        if ($conn->query($add_domain_column)) {
            error_log("Added domain column to licenses table");
        } else {
            error_log("Error adding domain column: " . $conn->error);
        }
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add new license
    if (isset($_POST['action']) && $_POST['action'] === 'add_license') {
        // Log all POST data for debugging
        error_log("Add license POST data: " . print_r($_POST, true));

        $license_key = isset($_POST['license_key']) ? trim($_POST['license_key']) : '';
        $pin = isset($_POST['pin']) ? trim($_POST['pin']) : '';
        $package = isset($_POST['package']) ? trim($_POST['package']) : 'platinum';
        $domain = isset($_POST['domain']) ? trim($_POST['domain']) : 'appystore.com';
        $max_devices = isset($_POST['max_devices']) ? (int)$_POST['max_devices'] : 1;
        $expires_at = isset($_POST['expires_at']) ? trim($_POST['expires_at']) : date('Y-m-d', strtotime('+1 year'));

        if (empty($license_key) || empty($pin)) {
            $message = 'License key and PIN are required';
            $message_type = 'danger';
        } else {
            // Check if license key already exists
            $stmt = $conn->prepare("SELECT id FROM licenses WHERE license_key = ?");
            $stmt->bind_param("s", $license_key);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $message = 'License key already exists';
                $message_type = 'danger';
            } else {
                // Check if domain column exists
                $check_domain_column = "SHOW COLUMNS FROM licenses LIKE 'domain'";
                $result = $conn->query($check_domain_column);
                $has_domain_column = ($result && $result->num_rows > 0);

                // Prepare the SQL statement based on whether domain column exists
                if ($has_domain_column) {
                    $sql = "INSERT INTO licenses (license_key, pin, package, domain, max_devices, expires_at) VALUES (?, ?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                } else {
                    $sql = "INSERT INTO licenses (license_key, pin, package, max_devices, expires_at) VALUES (?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                }

                if ($stmt) {
                    // Log the values for debugging
                    error_log("Adding license - Key: $license_key, PIN: $pin, Package: $package, Domain: $domain, Max Devices: $max_devices, Expires: $expires_at");

                    if ($has_domain_column) {
                        $stmt->bind_param("ssssis", $license_key, $pin, $package, $domain, $max_devices, $expires_at);
                    } else {
                        $stmt->bind_param("sssis", $license_key, $pin, $package, $max_devices, $expires_at);
                    }

                    if ($stmt->execute()) {
                        $message = 'License added successfully';
                        $message_type = 'success';
                    } else {
                        $message = 'Error executing statement: ' . $stmt->error;
                        $message_type = 'danger';
                        error_log("Error executing license insert: " . $stmt->error);
                    }
                } else {
                    $message = 'Error preparing statement: ' . $conn->error;
                    $message_type = 'danger';
                    error_log("Error preparing license insert: " . $conn->error);
                }
            }
        }
    }

    // Batch generate licenses
    if (isset($_POST['action']) && $_POST['action'] === 'batch_generate') {
        // Log all POST data for debugging
        error_log("Batch generate POST data: " . print_r($_POST, true));

        $count = isset($_POST['batch_count']) ? (int)$_POST['batch_count'] : 5;
        $prefix = isset($_POST['batch_prefix']) ? trim($_POST['batch_prefix']) : 'LIC-';
        $package = isset($_POST['batch_package']) ? trim($_POST['batch_package']) : 'platinum';
        $domain = isset($_POST['batch_domain']) ? trim($_POST['batch_domain']) : 'appystore.com';
        $max_devices = isset($_POST['batch_max_devices']) ? (int)$_POST['batch_max_devices'] : 1;
        $expires_at = isset($_POST['batch_expires_at']) ? trim($_POST['batch_expires_at']) : date('Y-m-d', strtotime('+1 year'));

        // Limit the number of licenses that can be generated at once
        if ($count > 100) {
            $count = 100;
        }

        $success_count = 0;
        $error_count = 0;
        $generated_licenses = [];

        // Start a transaction
        $conn->begin_transaction();

        try {
            for ($i = 0; $i < $count; $i++) {
                // Generate a unique license key
                $license_key = generateLicenseKey($prefix, 8);
                $pin = generatePIN(4);

                // Check if key already exists
                $stmt = $conn->prepare("SELECT id FROM licenses WHERE license_key = ?");
                $stmt->bind_param("s", $license_key);
                $stmt->execute();
                $result = $stmt->get_result();

                // If key exists, generate a new one (up to 5 attempts)
                $attempts = 0;
                while ($result->num_rows > 0 && $attempts < 5) {
                    $license_key = generateLicenseKey($prefix, 8);
                    $stmt->bind_param("s", $license_key);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $attempts++;
                }

                // If we still have a duplicate after 5 attempts, skip this one
                if ($result->num_rows > 0) {
                    $error_count++;
                    continue;
                }

                // Check if domain column exists
                $check_domain_column = "SHOW COLUMNS FROM licenses LIKE 'domain'";
                $result = $conn->query($check_domain_column);
                $has_domain_column = ($result && $result->num_rows > 0);

                // Prepare the SQL statement based on whether domain column exists
                if ($has_domain_column) {
                    $sql = "INSERT INTO licenses (license_key, pin, package, domain, max_devices, expires_at) VALUES (?, ?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                } else {
                    $sql = "INSERT INTO licenses (license_key, pin, package, max_devices, expires_at) VALUES (?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                }

                if ($stmt) {
                    // Log the values for debugging
                    error_log("Batch adding license - Key: $license_key, PIN: $pin, Package: $package, Domain: $domain, Max Devices: $max_devices, Expires: $expires_at");

                    if ($has_domain_column) {
                        $stmt->bind_param("ssssis", $license_key, $pin, $package, $domain, $max_devices, $expires_at);
                    } else {
                        $stmt->bind_param("sssis", $license_key, $pin, $package, $max_devices, $expires_at);
                    }

                    if ($stmt->execute()) {
                        $success_count++;
                        $generated_licenses[] = [
                            'license_key' => $license_key,
                            'pin' => $pin
                        ];
                    } else {
                        $error_count++;
                        error_log("Error executing batch license insert: " . $stmt->error);
                    }
                } else {
                    $error_count++;
                    error_log("Error preparing batch license insert: " . $conn->error);
                }
            }

            // Commit the transaction
            $conn->commit();

            if ($success_count > 0) {
                $message = "Successfully generated $success_count licenses";
                if ($error_count > 0) {
                    $message .= " ($error_count failed)";
                }
                $message_type = 'success';

                // Store generated licenses in session for display
                $_SESSION['generated_licenses'] = $generated_licenses;
            } else {
                $message = "Failed to generate licenses";
                $message_type = 'danger';
            }
        } catch (Exception $e) {
            // Rollback the transaction in case of error
            $conn->rollback();
            $message = "Error generating licenses: " . $e->getMessage();
            $message_type = 'danger';
        }
    }

    // Delete license
    if (isset($_POST['action']) && $_POST['action'] === 'delete_license') {
        $license_id = isset($_POST['license_id']) ? (int)$_POST['license_id'] : 0;

        if ($license_id > 0) {
            $stmt = $conn->prepare("DELETE FROM licenses WHERE id = ?");
            $stmt->bind_param("i", $license_id);

            if ($stmt->execute()) {
                $message = 'License deleted successfully';
                $message_type = 'success';
            } else {
                $message = 'Error deleting license: ' . $conn->error;
                $message_type = 'danger';
            }
        }
    }

    // Update license status
    if (isset($_POST['action']) && $_POST['action'] === 'update_status') {
        $license_id = isset($_POST['license_id']) ? (int)$_POST['license_id'] : 0;
        $status = isset($_POST['status']) ? (int)$_POST['status'] : 0;

        if ($license_id > 0) {
            $stmt = $conn->prepare("UPDATE licenses SET status = ? WHERE id = ?");
            $stmt->bind_param("ii", $status, $license_id);

            if ($stmt->execute()) {
                $message = 'License status updated successfully';
                $message_type = 'success';
            } else {
                $message = 'Error updating license status: ' . $conn->error;
                $message_type = 'danger';
            }
        }
    }
}

// Get all licenses
$licenses = []; // Initialize as empty array
if (!isset($db_error)) {
    $result = $conn->query("SELECT * FROM licenses ORDER BY created_at DESC");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $licenses[] = $row;
        }
    } else {
        // Log error
        $message = 'Error fetching licenses: ' . $conn->error;
        $message_type = 'danger';
    }
}

// Function to generate a random license key
function generateLicenseKey($prefix = 'LIC-', $length = 8) {
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $key = $prefix;

    for ($i = 0; $i < $length; $i++) {
        $key .= $characters[rand(0, strlen($characters) - 1)];
    }

    return $key;
}

// Function to generate a random PIN
function generatePIN($length = 4) {
    $pin = '';
    for ($i = 0; $i < $length; $i++) {
        $pin .= rand(0, 9);
    }
    return $pin;
}

// Add demo licenses if none exist
if (empty($licenses)) {
    // Insert demo licenses
    $demo_licenses = [
        ['DEMO-123456', '1234', 'platinum', 'appystore.com', 5, date('Y-m-d', strtotime('+1 year'))],
        ['DEMO-654321', '4321', 'gold', 'example.com', 3, date('Y-m-d', strtotime('+6 months'))],
        ['DEMO-111111', '1111', 'silver', '*************', 1, date('Y-m-d', strtotime('+3 months'))]
    ];

    // Check if domain column exists
    $check_domain_column = "SHOW COLUMNS FROM licenses LIKE 'domain'";
    $result = $conn->query($check_domain_column);
    $has_domain_column = ($result && $result->num_rows > 0);

    foreach ($demo_licenses as $license) {
        if ($has_domain_column) {
            $sql = "INSERT INTO licenses (license_key, pin, package, domain, max_devices, expires_at) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            if ($stmt) {
                $stmt->bind_param("ssssis", $license[0], $license[1], $license[2], $license[3], $license[4], $license[5]);
                $stmt->execute();
            } else {
                // Log error or set message
                $message = 'Error preparing statement: ' . $conn->error;
                $message_type = 'danger';
            }
        } else {
            $sql = "INSERT INTO licenses (license_key, pin, package, max_devices, expires_at) VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            if ($stmt) {
                $stmt->bind_param("sssis", $license[0], $license[1], $license[2], $license[4], $license[5]);
                $stmt->execute();
            } else {
                // Log error or set message
                $message = 'Error preparing statement: ' . $conn->error;
                $message_type = 'danger';
            }
        }
    }

    // Refresh licenses list
    $licenses = []; // Initialize as empty array
    $result = $conn->query("SELECT * FROM licenses ORDER BY created_at DESC");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $licenses[] = $row;
        }
    } else {
        // Log error or set message
        $message = 'Error refreshing licenses: ' . $conn->error;
        $message_type = 'danger';
    }
}

// Generate a license key if requested via AJAX
if (isset($_GET['action']) && $_GET['action'] === 'generate_key') {
    $prefix = isset($_GET['prefix']) ? $_GET['prefix'] : 'LIC-';
    $length = isset($_GET['length']) ? (int)$_GET['length'] : 8;

    $key = generateLicenseKey($prefix, $length);

    // Check if key already exists
    $stmt = $conn->prepare("SELECT id FROM licenses WHERE license_key = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();

    // If key exists, generate a new one
    while ($result->num_rows > 0) {
        $key = generateLicenseKey($prefix, $length);
        $stmt->bind_param("s", $key);
        $stmt->execute();
        $result = $stmt->get_result();
    }

    // Return the key as JSON
    header('Content-Type: application/json');
    echo json_encode(['key' => $key]);
    exit;
}

// Generate a PIN if requested via AJAX
if (isset($_GET['action']) && $_GET['action'] === 'generate_pin') {
    $length = isset($_GET['length']) ? (int)$_GET['length'] : 4;
    $pin = generatePIN($length);

    // Return the PIN as JSON
    header('Content-Type: application/json');
    echo json_encode(['pin' => $pin]);
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>License Management - AppyStore MRecharge Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/admin-style.css">
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: #f8f9fa;
        }
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .nav-link {
            font-weight: 500;
            color: #333;
        }
        .nav-link.active {
            color: #007bff;
        }
        main {
            padding-top: 48px;
        }
    </style>
</head>
<body>
    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#"><?php echo ADMIN_PANEL_TITLE; ?></a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="navbar-nav">
            <div class="nav-item dropdown text-nowrap">
                <a class="nav-link dropdown-toggle px-3" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-person-circle me-1"></i>
                    <?php echo htmlspecialchars($_SESSION['admin_full_name'] ?? $_SESSION['admin_username']); ?>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                    <li><h6 class="dropdown-header">
                        <i class="bi bi-person me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['admin_full_name']); ?>
                    </h6></li>
                    <li><small class="dropdown-item-text text-muted">
                        <i class="bi bi-envelope me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['admin_email']); ?>
                    </small></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                        <i class="bi bi-key me-2"></i>Change Password
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="?logout=1" onclick="return confirm('Are you sure you want to logout?')">
                        <i class="bi bi-box-arrow-right me-2"></i>Sign Out
                    </a></li>
                </ul>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3 sidebar-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="bi bi-speedometer2 me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="licenses.php">
                                <i class="bi bi-key me-2"></i>
                                Licenses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="devices.php">
                                <i class="bi bi-phone me-2"></i>
                                Devices
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="bi bi-gear me-2"></i>
                                Settings
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <hr class="text-muted">
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                <i class="bi bi-key-fill me-2"></i>
                                Change Password
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-danger" href="?logout=1" onclick="return confirm('Are you sure you want to logout?')">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">License Management</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addLicenseModal">
                            <i class="bi bi-plus-circle me-2"></i>
                            Add New License
                        </button>
                        <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#batchGenerateModal">
                            <i class="bi bi-list-check me-2"></i>
                            Batch Generate
                        </button>
                    </div>
                </div>

                <?php if (isset($db_error)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $db_error; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?>" role="alert">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($_POST['action']) && $_POST['action'] === 'add_license'): ?>
                    <div class="alert alert-info" role="alert">
                        <h5>Debug Info: Form submission received with action: <?php echo $_POST['action']; ?></h5>
                        <p>License Key: <?php echo isset($_POST['license_key']) ? $_POST['license_key'] : 'Not set'; ?></p>
                        <p>PIN: <?php echo isset($_POST['pin']) ? $_POST['pin'] : 'Not set'; ?></p>
                        <p>Package: <?php echo isset($_POST['package']) ? $_POST['package'] : 'Not set'; ?></p>
                        <p>Domain: <?php echo isset($_POST['domain']) ? $_POST['domain'] : 'Not set'; ?></p>
                        <p>Max Devices: <?php echo isset($_POST['max_devices']) ? $_POST['max_devices'] : 'Not set'; ?></p>
                        <p>Expires: <?php echo isset($_POST['expires_at']) ? $_POST['expires_at'] : 'Not set'; ?></p>
                    </div>
                <?php endif; ?>

                <?php if (isset($_POST['action']) && $_SERVER['REQUEST_METHOD'] === 'POST'): ?>
                    <!-- Debug information - Remove in production -->
                    <div class="alert alert-info small">
                        <p><strong>Debug Info:</strong> Form submission received with action: <?php echo htmlspecialchars($_POST['action']); ?></p>
                        <?php if ($_POST['action'] === 'add_license'): ?>
                            <p>
                                License Key: <?php echo htmlspecialchars(isset($_POST['license_key']) ? $_POST['license_key'] : 'Not set'); ?><br>
                                PIN: <?php echo htmlspecialchars(isset($_POST['pin']) ? $_POST['pin'] : 'Not set'); ?><br>
                                Package: <?php echo htmlspecialchars(isset($_POST['package']) ? $_POST['package'] : 'Not set'); ?><br>
                                Domain: <?php echo htmlspecialchars(isset($_POST['domain']) ? $_POST['domain'] : 'Not set'); ?><br>
                                Max Devices: <?php echo htmlspecialchars(isset($_POST['max_devices']) ? $_POST['max_devices'] : 'Not set'); ?><br>
                                Expires: <?php echo htmlspecialchars(isset($_POST['expires_at']) ? $_POST['expires_at'] : 'Not set'); ?>
                            </p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['generated_licenses']) && !empty($_SESSION['generated_licenses'])): ?>
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Recently Generated Licenses</h5>
                            <button type="button" class="btn-close" aria-label="Close" onclick="document.getElementById('generated-licenses-card').style.display='none';"></button>
                        </div>
                        <div class="card-body" id="generated-licenses-card">
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>License Key</th>
                                            <th>PIN</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($_SESSION['generated_licenses'] as $index => $license): ?>
                                            <tr>
                                                <td><?php echo $license['license_key']; ?></td>
                                                <td><?php echo $license['pin']; ?></td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary copy-btn"
                                                            data-license="<?php echo $license['license_key']; ?>"
                                                            data-pin="<?php echo $license['pin']; ?>">
                                                        <i class="bi bi-clipboard"></i> Copy
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-3">
                                <button type="button" class="btn btn-primary" id="exportCsvBtn">Export as CSV</button>
                                <button type="button" class="btn btn-secondary" onclick="document.getElementById('generated-licenses-card').style.display='none';">Close</button>
                            </div>
                        </div>
                    </div>
                    <?php unset($_SESSION['generated_licenses']); ?>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>License Key</th>
                                <th>PIN</th>
                                <th>Package</th>
                                <th>Domain</th>
                                <th>Max Devices</th>
                                <th>Status</th>
                                <th>Expires</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($licenses)): ?>
                                <tr>
                                    <td colspan="10" class="text-center">No licenses found</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($licenses as $license): ?>
                                    <tr>
                                        <td><?php echo $license['id']; ?></td>
                                        <td><?php echo $license['license_key']; ?></td>
                                        <td><?php echo $license['pin']; ?></td>
                                        <td><?php echo $license['package']; ?></td>
                                        <td><?php echo isset($license['domain']) ? $license['domain'] : 'appystore.com'; ?></td>
                                        <td><?php echo $license['max_devices']; ?></td>
                                        <td>
                                            <?php if ($license['status'] == 1): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $license['expires_at']; ?></td>
                                        <td><?php echo $license['created_at']; ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <form method="post" class="d-inline">
                                                    <input type="hidden" name="action" value="update_status">
                                                    <input type="hidden" name="license_id" value="<?php echo $license['id']; ?>">
                                                    <input type="hidden" name="status" value="<?php echo $license['status'] == 1 ? 0 : 1; ?>">
                                                    <button type="submit" class="btn btn-<?php echo $license['status'] == 1 ? 'warning' : 'success'; ?>" title="<?php echo $license['status'] == 1 ? 'Deactivate' : 'Activate'; ?>">
                                                        <i class="bi bi-<?php echo $license['status'] == 1 ? 'toggle-off' : 'toggle-on'; ?>"></i>
                                                    </button>
                                                </form>
                                                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#viewDevicesModal<?php echo $license['id']; ?>" title="View Devices">
                                                    <i class="bi bi-phone"></i>
                                                </button>
                                                <form method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this license?');">
                                                    <input type="hidden" name="action" value="delete_license">
                                                    <input type="hidden" name="license_id" value="<?php echo $license['id']; ?>">
                                                    <button type="submit" class="btn btn-danger" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </main>
        </div>
    </div>

    <!-- Add License Modal -->
    <div class="modal fade" id="addLicenseModal" tabindex="-1" aria-labelledby="addLicenseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addLicenseModalLabel">Add New License</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_license">
                        <div class="mb-3">
                            <label for="license_key" class="form-label">License Key</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="license_key" name="license_key" required>
                                <button class="btn btn-outline-secondary" type="button" id="generateLicenseBtn">Generate</button>
                            </div>
                            <div class="form-text">Enter a unique license key or click Generate to create one</div>
                        </div>
                        <div class="mb-3">
                            <label for="pin" class="form-label">PIN</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="pin" name="pin" required>
                                <button class="btn btn-outline-secondary" type="button" id="generatePinBtn">Generate</button>
                            </div>
                            <div class="form-text">Enter a PIN for this license or click Generate to create one</div>
                        </div>
                        <div class="mb-3">
                            <label for="key_prefix" class="form-label">License Key Prefix</label>
                            <select class="form-select" id="key_prefix">
                                <option value="LIC-">LIC-</option>
                                <option value="DEMO-">DEMO-</option>
                                <option value="PREMIUM-">PREMIUM-</option>
                                <option value="GOLD-">GOLD-</option>
                                <option value="SILVER-">SILVER-</option>
                                <option value="TRIAL-">TRIAL-</option>
                                <option value="CUSTOM-">CUSTOM-</option>
                            </select>
                            <div class="form-text">Select a prefix for generated license keys</div>
                        </div>
                        <div class="mb-3">
                            <label for="package" class="form-label">Package</label>
                            <select class="form-select" id="package" name="package">
                                <option value="platinum">Platinum</option>
                                <option value="gold">Gold</option>
                                <option value="silver">Silver</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="domain" class="form-label">Domain</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="domain" name="domain" value="appystore.com" required>
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">Select</button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item domain-option" href="#">appystore.com</a></li>
                                    <li><a class="dropdown-item domain-option" href="#">example.com</a></li>
                                    <li><a class="dropdown-item domain-option" href="#">yourdomain.com</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item domain-option" href="#">localhost</a></li>
                                    <li><a class="dropdown-item domain-option" href="#">*************</a></li>
                                </ul>
                            </div>
                            <div class="form-text">Enter the domain for this license</div>
                        </div>
                        <div class="mb-3">
                            <label for="max_devices" class="form-label">Max Devices</label>
                            <input type="number" class="form-control" id="max_devices" name="max_devices" value="1" min="1" max="100">
                            <div class="form-text">Maximum number of devices that can use this license</div>
                        </div>
                        <div class="mb-3">
                            <label for="expires_at" class="form-label">Expiration Date</label>
                            <input type="date" class="form-control" id="expires_at" name="expires_at" value="<?php echo date('Y-m-d', strtotime('+1 year')); ?>">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add License</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Batch Generate Modal -->
    <div class="modal fade" id="batchGenerateModal" tabindex="-1" aria-labelledby="batchGenerateModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="batchGenerateModalLabel">Batch Generate Licenses</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" id="batchGenerateForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="batch_generate">

                        <div class="mb-3">
                            <label for="batch_count" class="form-label">Number of Licenses</label>
                            <input type="number" class="form-control" id="batch_count" name="batch_count" value="5" min="1" max="100">
                            <div class="form-text">How many licenses to generate</div>
                        </div>

                        <div class="mb-3">
                            <label for="batch_prefix" class="form-label">License Key Prefix</label>
                            <select class="form-select" id="batch_prefix" name="batch_prefix">
                                <option value="LIC-">LIC-</option>
                                <option value="DEMO-">DEMO-</option>
                                <option value="PREMIUM-">PREMIUM-</option>
                                <option value="GOLD-">GOLD-</option>
                                <option value="SILVER-">SILVER-</option>
                                <option value="TRIAL-">TRIAL-</option>
                                <option value="CUSTOM-">CUSTOM-</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="batch_package" class="form-label">Package</label>
                            <select class="form-select" id="batch_package" name="batch_package">
                                <option value="platinum">Platinum</option>
                                <option value="gold">Gold</option>
                                <option value="silver">Silver</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="batch_domain" class="form-label">Domain</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="batch_domain" name="batch_domain" value="appystore.com" required>
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">Select</button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item batch-domain-option" href="#">appystore.com</a></li>
                                    <li><a class="dropdown-item batch-domain-option" href="#">example.com</a></li>
                                    <li><a class="dropdown-item batch-domain-option" href="#">yourdomain.com</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item batch-domain-option" href="#">localhost</a></li>
                                    <li><a class="dropdown-item batch-domain-option" href="#">*************</a></li>
                                </ul>
                            </div>
                            <div class="form-text">Enter the domain for these licenses</div>
                        </div>

                        <div class="mb-3">
                            <label for="batch_max_devices" class="form-label">Max Devices</label>
                            <input type="number" class="form-control" id="batch_max_devices" name="batch_max_devices" value="1" min="1" max="100">
                        </div>

                        <div class="mb-3">
                            <label for="batch_expires_at" class="form-label">Expiration Date</label>
                            <input type="date" class="form-control" id="batch_expires_at" name="batch_expires_at" value="<?php echo date('Y-m-d', strtotime('+1 year')); ?>">
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="generate_preview" checked>
                                <label class="form-check-label" for="generate_preview">
                                    Preview licenses before saving
                                </label>
                            </div>
                        </div>

                        <div id="preview_container" class="d-none">
                            <h6>Preview Generated Licenses</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>License Key</th>
                                            <th>PIN</th>
                                        </tr>
                                    </thead>
                                    <tbody id="preview_tbody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-info" id="previewBtn">Preview</button>
                        <button type="submit" class="btn btn-primary" id="generateBatchBtn">Generate</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Devices Modals -->
    <?php if (!empty($licenses)): ?>
        <?php foreach ($licenses as $license): ?>
            <div class="modal fade" id="viewDevicesModal<?php echo $license['id']; ?>" tabindex="-1" aria-labelledby="viewDevicesModalLabel<?php echo $license['id']; ?>" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="viewDevicesModalLabel<?php echo $license['id']; ?>">Devices Using License: <?php echo $license['license_key']; ?></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <?php
                            // Get devices using this license
                            $devices = [];
                            $stmt = $conn->prepare("SELECT DISTINCT device_id, device_info, MAX(created_at) as last_login FROM auth_logs WHERE license_key = ? GROUP BY device_id");
                            $stmt->bind_param("s", $license['license_key']);
                            $stmt->execute();
                            $result = $stmt->get_result();

                            if ($result->num_rows > 0) {
                                while ($row = $result->fetch_assoc()) {
                                    $devices[] = $row;
                                }
                            }
                            ?>

                            <?php if (empty($devices)): ?>
                                <p class="text-center">No devices have used this license yet.</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Device ID</th>
                                                <th>Device Info</th>
                                                <th>Last Login</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($devices as $device): ?>
                                                <tr>
                                                    <td><?php echo $device['device_id']; ?></td>
                                                    <td><?php echo $device['device_info']; ?></td>
                                                    <td><?php echo $device['last_login']; ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // License key generation
            const generateLicenseBtn = document.getElementById('generateLicenseBtn');
            const licenseKeyInput = document.getElementById('license_key');
            const keyPrefixSelect = document.getElementById('key_prefix');

            generateLicenseBtn.addEventListener('click', function() {
                const prefix = keyPrefixSelect.value;

                // Make AJAX request to generate license key
                fetch(`licenses.php?action=generate_key&prefix=${prefix}&length=8`)
                    .then(response => response.json())
                    .then(data => {
                        licenseKeyInput.value = data.key;
                    })
                    .catch(error => {
                        console.error('Error generating license key:', error);
                        alert('Error generating license key. Please try again.');
                    });
            });

            // PIN generation
            const generatePinBtn = document.getElementById('generatePinBtn');
            const pinInput = document.getElementById('pin');

            generatePinBtn.addEventListener('click', function() {
                // Make AJAX request to generate PIN
                fetch('licenses.php?action=generate_pin&length=4')
                    .then(response => response.json())
                    .then(data => {
                        pinInput.value = data.pin;
                    })
                    .catch(error => {
                        console.error('Error generating PIN:', error);
                        alert('Error generating PIN. Please try again.');
                    });
            });

            // Batch license generation preview
            const previewBtn = document.getElementById('previewBtn');
            const previewContainer = document.getElementById('preview_container');
            const previewTbody = document.getElementById('preview_tbody');
            const generatePreviewCheckbox = document.getElementById('generate_preview');
            const batchGenerateForm = document.getElementById('batchGenerateForm');
            const generateBatchBtn = document.getElementById('generateBatchBtn');

            // Toggle preview container based on checkbox
            generatePreviewCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    previewBtn.classList.remove('d-none');
                } else {
                    previewBtn.classList.add('d-none');
                    previewContainer.classList.add('d-none');
                }
            });

            // Preview batch generation
            previewBtn.addEventListener('click', function() {
                const count = document.getElementById('batch_count').value;
                const prefix = document.getElementById('batch_prefix').value;

                // Clear previous preview
                previewTbody.innerHTML = '';

                // Generate preview licenses
                for (let i = 0; i < count; i++) {
                    // Generate a random license key and PIN for preview
                    const randomKey = generateRandomKey(prefix, 8);
                    const randomPin = generateRandomPin(4);

                    // Add to preview table
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${randomKey}</td>
                        <td>${randomPin}</td>
                    `;
                    previewTbody.appendChild(row);
                }

                // Show preview container
                previewContainer.classList.remove('d-none');
            });

            // Helper function to generate a random license key for preview
            function generateRandomKey(prefix, length) {
                const characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                let key = prefix;

                for (let i = 0; i < length; i++) {
                    key += characters.charAt(Math.floor(Math.random() * characters.length));
                }

                return key;
            }

            // Helper function to generate a random PIN for preview
            function generateRandomPin(length) {
                let pin = '';
                for (let i = 0; i < length; i++) {
                    pin += Math.floor(Math.random() * 10);
                }
                return pin;
            }

            // Submit batch generation form
            batchGenerateForm.addEventListener('submit', function(e) {
                const count = document.getElementById('batch_count').value;

                if (count > 20) {
                    if (!confirm(`You are about to generate ${count} licenses. Are you sure you want to continue?`)) {
                        e.preventDefault();
                        return false;
                    }
                }

                // Show loading state
                generateBatchBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Generating...';
                generateBatchBtn.disabled = true;

                // Form will submit normally
            });

            // Domain selection for single license
            document.querySelectorAll('.domain-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('domain').value = this.textContent;
                });
            });

            // Domain selection for batch licenses
            document.querySelectorAll('.batch-domain-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('batch_domain').value = this.textContent;
                });
            });

            // Copy license and PIN to clipboard
            document.querySelectorAll('.copy-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const license = this.getAttribute('data-license');
                    const pin = this.getAttribute('data-pin');
                    const textToCopy = `License Key: ${license}\nPIN: ${pin}`;

                    navigator.clipboard.writeText(textToCopy)
                        .then(() => {
                            // Change button text temporarily
                            const originalText = this.innerHTML;
                            this.innerHTML = '<i class="bi bi-check"></i> Copied!';
                            setTimeout(() => {
                                this.innerHTML = originalText;
                            }, 2000);
                        })
                        .catch(err => {
                            console.error('Failed to copy: ', err);
                            alert('Failed to copy to clipboard');
                        });
                });
            });

            // Export licenses as CSV
            const exportCsvBtn = document.getElementById('exportCsvBtn');
            if (exportCsvBtn) {
                exportCsvBtn.addEventListener('click', function() {
                    // Get all license rows
                    const rows = document.querySelectorAll('#generated-licenses-card tbody tr');
                    let csvContent = "License Key,PIN\n";

                    // Add each license to CSV content
                    rows.forEach(row => {
                        const licenseKey = row.cells[0].textContent.trim();
                        const pin = row.cells[1].textContent.trim();
                        csvContent += `"${licenseKey}","${pin}"\n`;
                    });

                    // Create a blob and download link
                    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.setAttribute('href', url);
                    link.setAttribute('download', 'licenses.csv');
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                });
            }
        });
    </script>

    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="changePasswordModalLabel">
                        <i class="bi bi-key me-2"></i>Change Password
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="dashboard.php" id="changePasswordForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="change_password">

                        <div class="mb-3">
                            <label for="current_password" class="form-label">
                                <i class="bi bi-lock me-1"></i>Current Password
                            </label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">
                                <i class="bi bi-key me-1"></i>New Password
                            </label>
                            <input type="password" class="form-control" id="new_password" name="new_password"
                                   required minlength="<?php echo ADMIN_PASSWORD_MIN_LENGTH; ?>">
                            <div class="form-text">
                                Password must be at least <?php echo ADMIN_PASSWORD_MIN_LENGTH; ?> characters long.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="bi bi-check-circle me-1"></i>Confirm New Password
                            </label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="invalid-feedback" id="password-mismatch">
                                Passwords do not match.
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Security Note:</strong> You will remain logged in after changing your password.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="changePasswordBtn">
                            <i class="bi bi-check-lg me-1"></i>Change Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
