<?php
/**
 * Admin Panel - Settings
 *
 * This file handles system settings for the admin panel.
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include configuration
require_once 'config.php';

// Check if user is logged in
if (!checkAdminSession()) {
    header('Location: login.php');
    exit;
}

// Get database connection
$conn = getDbConnection();
$db_error = null;

// Initialize variables
$message = '';
$message_type = '';
$settings = [
    'app_version' => '1.0',
    'min_app_version' => '1.0',
    'allow_demo_licenses' => '1',
    'max_devices_per_license' => '5',
    'license_duration_days' => '365',
    'use_https' => '0'
];

// Create settings table if it doesn't exist
if (!isset($db_error)) {
    $create_table_sql = "CREATE TABLE IF NOT EXISTS settings (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(255) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    $conn->query($create_table_sql);

    // Check if settings exist, if not insert default settings
    $result = $conn->query("SELECT COUNT(*) as count FROM settings");
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        foreach ($settings as $key => $value) {
            $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)");
            $stmt->bind_param("ss", $key, $value);
            $stmt->execute();
        }
    }

    // Get current settings
    $result = $conn->query("SELECT * FROM settings");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update_settings') {
        // Update settings
        $updated_settings = [
            'app_version' => isset($_POST['app_version']) ? trim($_POST['app_version']) : '1.0',
            'min_app_version' => isset($_POST['min_app_version']) ? trim($_POST['min_app_version']) : '1.0',
            'allow_demo_licenses' => isset($_POST['allow_demo_licenses']) ? '1' : '0',
            'max_devices_per_license' => isset($_POST['max_devices_per_license']) ? (int)$_POST['max_devices_per_license'] : 5,
            'license_duration_days' => isset($_POST['license_duration_days']) ? (int)$_POST['license_duration_days'] : 365,
            'use_https' => isset($_POST['use_https']) ? '1' : '0'
        ];

        foreach ($updated_settings as $key => $value) {
            $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
            $stmt->bind_param("sss", $key, $value, $value);
            $stmt->execute();
        }

        $message = 'Settings updated successfully';
        $message_type = 'success';

        // Update settings array
        $settings = $updated_settings;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - AppyStore MRecharge Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/admin-style.css">
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: #f8f9fa;
        }
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .nav-link {
            font-weight: 500;
            color: #333;
        }
        .nav-link.active {
            color: #007bff;
        }
        main {
            padding-top: 48px;
        }
    </style>
</head>
<body>
    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#">AppyStore MRecharge</a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="navbar-nav">
            <div class="nav-item dropdown text-nowrap">
                <a class="nav-link dropdown-toggle px-3" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-person-circle me-1"></i>
                    <?php echo htmlspecialchars($_SESSION['admin_full_name'] ?? $_SESSION['admin_username']); ?>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                    <li><h6 class="dropdown-header">
                        <i class="bi bi-person me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['admin_full_name']); ?>
                    </h6></li>
                    <li><small class="dropdown-item-text text-muted">
                        <i class="bi bi-envelope me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['admin_email']); ?>
                    </small></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                        <i class="bi bi-key me-2"></i>Change Password
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="?logout=1" onclick="return confirm('Are you sure you want to logout?')">
                        <i class="bi bi-box-arrow-right me-2"></i>Sign Out
                    </a></li>
                </ul>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3 sidebar-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="bi bi-speedometer2 me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="licenses.php">
                                <i class="bi bi-key me-2"></i>
                                Licenses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="devices.php">
                                <i class="bi bi-phone me-2"></i>
                                Devices
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="settings.php">
                                <i class="bi bi-gear me-2"></i>
                                Settings
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <hr class="text-muted">
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                <i class="bi bi-key-fill me-2"></i>
                                Change Password
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-danger" href="?logout=1" onclick="return confirm('Are you sure you want to logout?')">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">System Settings</h1>
                </div>

                <?php if (isset($db_error)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $db_error; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?>" role="alert">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Application Settings</h5>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <input type="hidden" name="action" value="update_settings">

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="app_version" class="form-label">Current App Version</label>
                                    <input type="text" class="form-control" id="app_version" name="app_version" value="<?php echo $settings['app_version']; ?>">
                                    <div class="form-text">The current version of the app</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="min_app_version" class="form-label">Minimum App Version</label>
                                    <input type="text" class="form-control" id="min_app_version" name="min_app_version" value="<?php echo $settings['min_app_version']; ?>">
                                    <div class="form-text">The minimum version required (older versions will be prompted to update)</div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="max_devices_per_license" class="form-label">Max Devices Per License</label>
                                    <input type="number" class="form-control" id="max_devices_per_license" name="max_devices_per_license" value="<?php echo $settings['max_devices_per_license']; ?>" min="1" max="100">
                                    <div class="form-text">Maximum number of devices allowed per license by default</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="license_duration_days" class="form-label">Default License Duration (days)</label>
                                    <input type="number" class="form-control" id="license_duration_days" name="license_duration_days" value="<?php echo $settings['license_duration_days']; ?>" min="1" max="3650">
                                    <div class="form-text">Default duration for new licenses in days</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="allow_demo_licenses" name="allow_demo_licenses" <?php echo $settings['allow_demo_licenses'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="allow_demo_licenses">Allow Demo Licenses</label>
                                </div>
                                <div class="form-text">If enabled, license keys starting with "DEMO-" will be automatically accepted</div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="use_https" name="use_https" <?php echo $settings['use_https'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="use_https">Use HTTPS</label>
                                </div>
                                <div class="form-text">If enabled, the app will use HTTPS for API connections</div>
                            </div>

                            <button type="submit" class="btn btn-primary">Save Settings</button>
                        </form>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">API Information</h5>
                    </div>
                    <div class="card-body">
                        <h6>Device Authentication API Endpoint</h6>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" value="<?php echo $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/device_auth.php'; ?>" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard(this.previousElementSibling)">Copy</button>
                        </div>

                        <h6 class="mt-4">Required Parameters</h6>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Description</th>
                                        <th>Example</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>license_key</code></td>
                                        <td>The license key to validate</td>
                                        <td><code>DEMO-123456</code></td>
                                    </tr>
                                    <tr>
                                        <td><code>pin</code></td>
                                        <td>The PIN associated with the license</td>
                                        <td><code>1234</code></td>
                                    </tr>
                                    <tr>
                                        <td><code>device_id</code></td>
                                        <td>Unique identifier for the device</td>
                                        <td><code>**********</code></td>
                                    </tr>
                                    <tr>
                                        <td><code>device_info</code></td>
                                        <td>Information about the device</td>
                                        <td><code>Samsung SM-G970F V:10</code></td>
                                    </tr>
                                    <tr>
                                        <td><code>type</code></td>
                                        <td>Type of request</td>
                                        <td><code>server</code></td>
                                    </tr>
                                    <tr>
                                        <td><code>pkg</code></td>
                                        <td>Package type</td>
                                        <td><code>platinum</code></td>
                                    </tr>
                                    <tr>
                                        <td><code>version</code></td>
                                        <td>App version</td>
                                        <td><code>20</code></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <h6 class="mt-4">Response Format</h6>
                        <pre class="bg-light p-3 rounded"><code>[
    {
        "status": 1,
        "version": 1,
        "message": "License validated successfully",
        "domain": "appystore.com",
        "sec": 0,
        "expires": "2023-12-31",
        "package": "platinum",
        "device_id": "**********"
    }
]</code></pre>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="changePasswordModalLabel">
                        <i class="bi bi-key me-2"></i>Change Password
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="dashboard.php" id="changePasswordForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="change_password">

                        <div class="mb-3">
                            <label for="current_password" class="form-label">
                                <i class="bi bi-lock me-1"></i>Current Password
                            </label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">
                                <i class="bi bi-key me-1"></i>New Password
                            </label>
                            <input type="password" class="form-control" id="new_password" name="new_password"
                                   required minlength="<?php echo ADMIN_PASSWORD_MIN_LENGTH; ?>">
                            <div class="form-text">
                                Password must be at least <?php echo ADMIN_PASSWORD_MIN_LENGTH; ?> characters long.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="bi bi-check-circle me-1"></i>Confirm New Password
                            </label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="invalid-feedback" id="password-mismatch">
                                Passwords do not match.
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Security Note:</strong> You will remain logged in after changing your password.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="changePasswordBtn">
                            <i class="bi bi-check-lg me-1"></i>Change Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard(element) {
            element.select();
            document.execCommand('copy');

            // Show a temporary "Copied!" tooltip
            const originalText = element.nextElementSibling.innerText;
            element.nextElementSibling.innerText = 'Copied!';
            setTimeout(() => {
                element.nextElementSibling.innerText = originalText;
            }, 2000);
        }
    </script>
</body>
</html>
